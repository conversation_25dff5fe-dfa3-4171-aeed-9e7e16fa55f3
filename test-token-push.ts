#!/usr/bin/env tsx

/**
 * TypeScript Test Script für Token Push API
 * Pusht den Token "XX123456" für die OU "Gesellschaft für Gestaltung"
 * 
 * Ausführung: npx tsx test-token-push.ts
 */

// Konfiguration
const CONFIG = {
  API_BASE_URL: 'http://localhost:3000', // Anpassen je nach Umgebung
  API_KEY: 'YOUR_API_KEY_HERE', // Hier den echten API-Key eintragen
  TARGET_OU: 'Gesellschaft für Gestaltung',
  TEST_TOKEN: {
    name: "Test Token Gesellschaft für Gestaltung",
    uid: "XX123456",
    note: "Test Token erstellt via TypeScript Script"
  }
};

// Types
interface Contact {
  id: string;
  name?: string;
  companyName?: string;
  cpo: boolean;
  ou?: {
    id: string;
    name: string;
    code: string;
  };
}

interface ApiKeyValidationResponse {
  success: boolean;
  message: string;
  timestamp: string;
  contact: Contact;
}

interface TokenPushResponse {
  success: boolean;
  message: string;
  contact: Contact;
  tokenGroup: {
    id: string;
    name: string;
  };
  processedTokens: Array<{
    id: string;
    name: string;
    authenticationId: string;
    action: 'created' | 'updated';
  }>;
  errors: Array<{
    uid: string;
    name: string;
    error: string;
  }>;
  longship: {
    success: Array<{
      uid: string;
      name: string;
      longshipTokenGroupId: string;
    }>;
    errors: Array<{
      uid?: string;
      name?: string;
      error: string;
    }>;
    statistics: {
      pushed: number;
      failed: number;
    };
  };
  statistics: {
    total: number;
    processed: number;
    errors: number;
    longshipPushed: number;
    longshipErrors: number;
  };
  timestamp: string;
}

// Utility Functions
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

const log = {
  info: (msg: string) => console.log(`${colors.blue}🔍 ${msg}${colors.reset}`),
  success: (msg: string) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg: string) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg: string) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  header: (msg: string) => console.log(`${colors.cyan}🧪 ${msg}${colors.reset}`),
};

// API Functions
async function makeApiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<{ success: boolean; data?: T; error?: string; status: number }> {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${CONFIG.API_KEY}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();
    
    return {
      success: response.ok,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || data.error || 'Unknown error',
      status: response.status,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error',
      status: 0,
    };
  }
}

async function testApiKeyValidation(): Promise<boolean> {
  log.info('Teste API-Key Validierung...');
  
  const result = await makeApiRequest<ApiKeyValidationResponse>('/api/v1/test/apikey');
  
  if (result.success && result.data) {
    log.success('API-Key Validierung erfolgreich');
    
    const contact = result.data.contact;
    console.log('📋 Contact Info:', {
      id: contact.id,
      name: contact.name,
      companyName: contact.companyName,
      ou: contact.ou?.name
    });
    
    if (contact.ou?.name === CONFIG.TARGET_OU) {
      log.success(`Korrekte OU gefunden: "${CONFIG.TARGET_OU}"`);
      return true;
    } else {
      log.error(`Falsche OU: "${contact.ou?.name}" (erwartet: "${CONFIG.TARGET_OU}")`);
      return false;
    }
  } else {
    log.error(`API-Key Validierung fehlgeschlagen (${result.status}): ${result.error}`);
    return false;
  }
}

async function pushToken(): Promise<boolean> {
  log.info('Pushe Token...');
  
  const payload = {
    tokens: [CONFIG.TEST_TOKEN]
  };
  
  console.log('📦 Token Daten:', JSON.stringify(payload, null, 2));
  
  const result = await makeApiRequest<TokenPushResponse>('/api/v1/contact/tokens', {
    method: 'POST',
    body: JSON.stringify(payload),
  });
  
  if (result.success && result.data) {
    log.success('Token Push erfolgreich!');
    
    const data = result.data;
    console.log('📊 Ergebnis:', {
      message: data.message,
      tokenGroup: data.tokenGroup,
      statistics: data.statistics,
      longshipStats: data.longship.statistics
    });
    
    // Details der verarbeiteten Tokens
    if (data.processedTokens.length > 0) {
      console.log('\n📝 Verarbeitete Tokens:');
      data.processedTokens.forEach(token => {
        console.log(`  - ${token.name} (${token.authenticationId}) - ${token.action}`);
      });
    }
    
    // Longship Ergebnisse
    if (data.longship.success.length > 0) {
      console.log('\n🌐 Longship Push erfolgreich:');
      data.longship.success.forEach(token => {
        console.log(`  - ${token.name} (${token.uid}) -> TokenGroup: ${token.longshipTokenGroupId}`);
      });
    }
    
    // Fehler anzeigen
    if (data.errors.length > 0) {
      log.warning('Lokale Fehler gefunden:');
      data.errors.forEach(error => {
        console.log(`  - ${error.name} (${error.uid}): ${error.error}`);
      });
    }
    
    if (data.longship.errors.length > 0) {
      log.warning('Longship Fehler gefunden:');
      data.longship.errors.forEach(error => {
        console.log(`  - ${error.name || error.uid}: ${error.error}`);
      });
    }
    
    return true;
  } else {
    log.error(`Token Push fehlgeschlagen (${result.status}): ${result.error}`);
    return false;
  }
}

async function main(): Promise<void> {
  log.header('Token Push Test Script');
  console.log('='.repeat(50));
  
  // Prüfen ob API-Key gesetzt ist
  if (CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
    log.error('Bitte setzen Sie einen gültigen API-Key in CONFIG.API_KEY');
    process.exit(1);
  }
  
  try {
    // 1. API-Key validieren
    const isValidApiKey = await testApiKeyValidation();
    if (!isValidApiKey) {
      log.error('API-Key Validierung fehlgeschlagen. Script wird beendet.');
      process.exit(1);
    }
    
    console.log('');
    
    // 2. Token pushen
    const isPushSuccessful = await pushToken();
    if (isPushSuccessful) {
      console.log('');
      log.success('🎉 Test erfolgreich abgeschlossen!');
      process.exit(0);
    } else {
      console.log('');
      log.error('Test fehlgeschlagen!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Unerwarteter Fehler: ${error instanceof Error ? error.message : error}`);
    process.exit(1);
  }
}

// Script ausführen
main();
