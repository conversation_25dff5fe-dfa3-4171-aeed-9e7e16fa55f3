# Forecast Aggregation API Documentation

## Overview
The Forecast Aggregation API provides endpoints for aggregating monthly forecast data to improve performance of forecast charts and dashboards.

## Endpoints

### 1. Aggregate All OUs Monthly Data
**POST** `/api/forecast/aggregate-monthly`

Aggregates monthly forecast data for all OUs for the last 24 months.

**Features:**
- Skips existing data for past months (only re-aggregates current month)
- Processes all OUs automatically
- Requires Admin role or internal API key

**Headers:**
```
Content-Type: application/json
x-api-key: internal-cronjob-key (for automated calls)
```

**Response:**
```json
{
  "status": "success",
  "message": "Monthly forecast aggregation completed successfully",
  "data": {
    "totalProcessed": 120,
    "totalSkipped": 95,
    "totalUpdated": 25,
    "ousProcessed": 5
  }
}
```

### 2. Aggregate Single OU Monthly Data
**POST** `/api/forecast/aggregate-single-ou`

Aggregates monthly forecast data for a specific OU.

**Body:**
```json
{
  "ouCode": "0001",
  "months": ["2024-01", "2024-02"] // Optional: specific months to process
}
```

**Features:**
- Intelligent skipping of existing data (except current month)
- Optional month specification
- Detailed processing statistics

**Response:**
```json
{
  "status": "success",
  "message": "Single OU aggregation completed successfully for 0001",
  "data": {
    "ouCode": "0001",
    "totalProcessed": 24,
    "totalSkipped": 20,
    "totalUpdated": 4,
    "monthsRequested": 24
  }
}
```

### 3. Check Missing Months for OU
**GET** `/api/forecast/missing-months?ouCode=0001`

Checks which months are missing aggregation data for a specific OU.

**Parameters:**
- `ouCode` (required): The OU code to check

**Response:**
```json
{
  "status": "success",
  "data": {
    "ouCode": "0001",
    "totalExpectedMonths": 24,
    "existingMonths": 20,
    "missingMonths": 4,
    "currentMonth": "2024-06",
    "missingMonthsList": ["2024-01", "2024-02", "2024-03", "2024-04"],
    "existingAggregations": [
      {
        "month": "2024-05",
        "isCurrentMonth": false,
        "hasData": true,
        "lastUpdated": "2024-06-18T10:30:00Z",
        "created": "2024-06-01T00:00:00Z"
      }
    ]
  }
}
```

### 4. Get OU List with Aggregation Status
**GET** `/api/forecast/ou-list`

Returns all OUs with their aggregation status.

**Response:**
```json
{
  "status": "success",
  "data": {
    "ous": [
      {
        "id": "1",
        "code": "0001",
        "name": "Main Office",
        "aggregationStatus": {
          "totalExpected": 24,
          "existing": 20,
          "missing": 4,
          "completionPercentage": 83,
          "hasCurrentMonth": true,
          "latestMonth": "2024-06",
          "lastUpdated": "2024-06-18T10:30:00Z",
          "hasData": true
        }
      }
    ],
    "summary": {
      "totalOus": 5,
      "fullyAggregated": 2,
      "partiallyAggregated": 2,
      "notAggregated": 1
    }
  }
}
```

### 5. Get Monthly Data for Charts
**GET** `/api/forecast/monthly-data`

Returns aggregated monthly data for the current user's OU scope.

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "month": "2024-01",
      "revenue": 15000.50,
      "energyGrossMargin": 12000.30,
      "thgRevenue": 500.20,
      "totalGrossMargin": 12500.50,
      "kWh": 10004.0,
      "sessions": 150
    }
  ]
}
```

### 6. Get Current Forecast Data
**GET** `/api/forecast/current-data`

Returns current year and month forecast calculations.

**Parameters:**
- `ouId` (optional): Specific OU ID to calculate forecasts for

**Response:**
```json
{
  "status": "success",
  "data": {
    "thgUntilNow": 1500.0,
    "energyGrossMarginUntilNow": 45000.0,
    "kWhUntilNow": 30000.0,
    "thgCurrentYear": 3000.0,
    "energyGrossMarginCurrentYear": 90000.0,
    "grossMarginCurrentYear": 93000.0,
    "kWhCurrentYear": 60000.0,
    "thgCurrentMonth": 250.0,
    "energyGrossMarginCurrentMonth": 7500.0,
    "grossMarginCurrentMonth": 7750.0,
    "kWhCurrentMonth": 5000.0
  }
}
```

### 7. Get Power Contract Warnings
**GET** `/api/forecast/power-contract-warnings`

Returns warnings about locations without valid power contracts that affect margin calculations.

**Parameters:**
- `ouId` (optional): Specific OU ID to check warnings for

**Response:**
```json
{
  "status": "success",
  "data": {
    "warningLevel": "medium",
    "summary": {
      "totalLocations": 10,
      "locationsWithoutContracts": 3,
      "percentageWithoutContracts": 30,
      "recentCdrsCount": 25,
      "recentCdrsVolume": 150.75
    },
    "locationsWithoutContracts": [
      {
        "id": "loc123",
        "name": "Hauptbahnhof",
        "city": "Berlin",
        "ouName": "Hauptverwaltung",
        "ouCode": "0001",
        "evseCount": 4,
        "activeEvses": 3
      }
    ]
  }
}
```

**Warning Levels:**
- `none`: No locations without contracts
- `low`: Few locations without contracts, minimal impact
- `medium`: Some locations without contracts, moderate impact
- `high`: Many locations without contracts or high charging activity

## Authentication
- All endpoints require authentication
- Admin role required for aggregation endpoints
- Internal API key supported for automated calls

## Automation
- Daily cronjob runs at 00:00 UTC
- Manual triggers available via Command page
- Intelligent skipping prevents duplicate processing

## Performance Considerations
- Aggregation improves chart loading times significantly
- Current month is always re-aggregated for accuracy
- Past months are skipped if data already exists
- Batch processing for multiple OUs

## Error Handling
All endpoints return consistent error responses:
```json
{
  "status": "error",
  "message": "Error description",
  "error": "Detailed error information"
}
```

## Usage Examples

### Command Page Integration
The Command page provides a user-friendly interface for:
- Running full aggregation for all OUs
- Aggregating specific OUs with OU selection
- Checking missing months before aggregation
- Viewing aggregation status and results

### Automated Processing
```bash
# Daily cronjob call
curl -X POST http://localhost:3000/api/forecast/aggregate-monthly \
  -H "x-api-key: internal-cronjob-key"
```

### Manual Single OU Processing
```bash
# Process specific OU
curl -X POST http://localhost:3000/api/forecast/aggregate-single-ou \
  -H "Content-Type: application/json" \
  -d '{"ouCode": "0001"}'
```
