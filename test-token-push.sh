#!/bin/bash

# Test Script für Token Push API
# Pusht den Token "XX123456" für die OU "Gesellschaft für Gestaltung"

# Konfiguration
API_BASE_URL="http://localhost:3000"  # Anpassen je nach Umgebung
API_KEY="YOUR_API_KEY_HERE"           # Hier den echten API-Key eintragen

# Farben für Output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funktionen
print_header() {
    echo -e "${BLUE}🧪 Token Push Test Script${NC}"
    echo "=================================================="
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}🔍 $1${NC}"
}

# API-Key prüfen
check_api_key() {
    if [ "$API_KEY" = "YOUR_API_KEY_HERE" ]; then
        print_error "Bitte setzen Sie einen gültigen API-Key in der Variable API_KEY"
        exit 1
    fi
}

# API-Key Validierung testen
test_api_key_validation() {
    print_info "Teste API-Key Validierung..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -H "Authorization: Bearer $API_KEY" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/api/v1/test/apikey")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        print_success "API-Key Validierung erfolgreich"
        
        # OU Name extrahieren (vereinfacht)
        ou_name=$(echo "$body" | grep -o '"name":"[^"]*"' | head -1 | cut -d'"' -f4)
        echo "📋 OU: $ou_name"
        
        if [[ "$body" == *"Gesellschaft für Gestaltung"* ]]; then
            print_success "Korrekte OU gefunden: 'Gesellschaft für Gestaltung'"
            return 0
        else
            print_error "Falsche OU gefunden (erwartet: 'Gesellschaft für Gestaltung')"
            return 1
        fi
    else
        print_error "API-Key Validierung fehlgeschlagen (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
}

# Token pushen
push_token() {
    print_info "Pushe Token XX123456..."
    
    # JSON Payload
    json_payload='{
        "tokens": [
            {
                "name": "Test Token Gesellschaft für Gestaltung",
                "uid": "XX123456",
                "note": "Test Token erstellt via Bash Script"
            }
        ]
    }'
    
    echo "📦 Token Daten:"
    echo "$json_payload" | jq . 2>/dev/null || echo "$json_payload"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $API_KEY" \
        -H "Content-Type: application/json" \
        -d "$json_payload" \
        "$API_BASE_URL/api/v1/contact/tokens")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        print_success "Token Push erfolgreich!"
        
        # Ergebnis formatiert ausgeben (wenn jq verfügbar ist)
        if command -v jq &> /dev/null; then
            echo "📊 Ergebnis:"
            echo "$body" | jq '{
                message: .message,
                tokenGroup: .tokenGroup,
                statistics: .statistics,
                longshipStats: .longship.statistics
            }'
            
            # Verarbeitete Tokens
            processed_count=$(echo "$body" | jq '.processedTokens | length' 2>/dev/null)
            if [ "$processed_count" -gt 0 ]; then
                echo "📝 Verarbeitete Tokens:"
                echo "$body" | jq -r '.processedTokens[] | "  - \(.name) (\(.authenticationId)) - \(.action)"'
            fi
            
            # Longship Erfolge
            longship_success_count=$(echo "$body" | jq '.longship.success | length' 2>/dev/null)
            if [ "$longship_success_count" -gt 0 ]; then
                echo "🌐 Longship Push erfolgreich:"
                echo "$body" | jq -r '.longship.success[] | "  - \(.name) (\(.uid)) -> TokenGroup: \(.longshipTokenGroupId)"'
            fi
            
            # Fehler
            error_count=$(echo "$body" | jq '.errors | length' 2>/dev/null)
            if [ "$error_count" -gt 0 ]; then
                print_warning "Lokale Fehler gefunden:"
                echo "$body" | jq -r '.errors[] | "  - \(.name) (\(.uid)): \(.error)"'
            fi
            
            longship_error_count=$(echo "$body" | jq '.longship.errors | length' 2>/dev/null)
            if [ "$longship_error_count" -gt 0 ]; then
                print_warning "Longship Fehler gefunden:"
                echo "$body" | jq -r '.longship.errors[] | "  - \(.name // .uid): \(.error)"'
            fi
        else
            echo "📊 Rohe Antwort (jq nicht verfügbar):"
            echo "$body"
        fi
        
        return 0
    else
        print_error "Token Push fehlgeschlagen (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
}

# Hauptfunktion
main() {
    print_header
    
    # API-Key prüfen
    check_api_key
    
    # 1. API-Key validieren
    if ! test_api_key_validation; then
        print_error "API-Key Validierung fehlgeschlagen. Script wird beendet."
        exit 1
    fi
    
    echo ""
    
    # 2. Token pushen
    if push_token; then
        echo ""
        print_success "🎉 Test erfolgreich abgeschlossen!"
        exit 0
    else
        echo ""
        print_error "❌ Test fehlgeschlagen!"
        exit 1
    fi
}

# Script ausführen
main "$@"
