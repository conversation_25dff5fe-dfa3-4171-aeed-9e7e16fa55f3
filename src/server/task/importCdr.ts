import AdmZip from "adm-zip";
import Logger from "~/server/logger/logger";
import { LongshipHeaders } from "~/utils/longship";
import * as fs from "fs";
import prisma from "../db/prisma";
import type { Cdr } from "@prisma/client";
import { LogType, NotificationType } from "@prisma/client";
import { env } from "../../env.js";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

import type { ApiResponse } from "~/types/api/apiType";
import {
  computeAdhocCosts,
  computeCardHolderCosts,
  computeDirectPaymentCosts,
  computeRoamingCostByEmpPrice,
} from "~/server/invoice/calculateCost";

export enum COPY_MODE {
  FULL = "full",
  DELTA = "delta",
}

// Typdefinition für die Importstatistik
export interface ImportStats {
  totalProcessed: number;
  successfullyImported: number;
  errors: string[];
}

export const durationToSeconds = (durationString: string): number => {
  const matches = durationString.match(/(\d+):(\d+):(\d+)/);
  if (!matches) {
    throw new Error(`Invalid format for durationToSeconds ${durationString}`);
  }
  const hours = parseInt(matches[1] || "0");
  const minutes = parseInt(matches[2] || "0");
  const seconds = parseInt(matches[3] || "0");
  const duration = hours * 3600 + minutes * 60 + seconds;
  if (duration < 0) {
    throw new Error("duration is low zero");
  }
  return duration;
};

function toBuffer(ab: ArrayBuffer) {
  const buf = Buffer.alloc(ab.byteLength);
  const view = new Uint8Array(ab);
  for (let i = 0; i < buf.length; ++i) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    buf[i] = view[i];
  }
  return buf;
}

type DbColumnType = "VARCHAR" | "DATETIME" | "INT" | "FLOAT" | "TINYINT" | "CHAR";

interface LongshipDbMapping {
  [key: string]: {
    type: DbColumnType;
    length?: DbColumnType extends infer U
      ? U extends "VARCHAR" | "CHAR" | "INT"
        ? number
        : undefined
      : undefined;
    name: string;
  };
}

const longshipDbMapping: LongshipDbMapping = {
  CDR_ID: {
    type: "VARCHAR",
    length: 191,
    name: "CDR_ID",
  },
  Start_datetime: {
    type: "DATETIME",
    name: "Start_datetime",
  },
  End_datetime: {
    type: "DATETIME",
    name: "Start_datetime",
  },
  Duration: {
    type: "VARCHAR",
    name: "Duration",
    length: 200,
  },
  DurationInSec: {
    type: "INT",
    name: "DurationInSec",
    length: 11,
  },
  Volume: {
    type: "FLOAT",
    name: "Volume",
  },
  Charge_Point_Address: {
    type: "VARCHAR",
    name: "Charge_Point_Address",
    length: 200,
  },
  Charge_Point_ZIP: {
    type: "VARCHAR",
    name: "Charge_Point_ZIP",
    length: 200,
  },
  Charge_Point_City: {
    type: "VARCHAR",
    name: "Charge_Point_City",
    length: 200,
  },
  Charge_Point_Country: {
    type: "VARCHAR",
    name: "Charge_Point_Country",
    length: 200,
  },
  Charge_Point_Type: {
    type: "TINYINT",
    name: "Charge_Point_Type",
    length: 4,
  },
  Product_Type: {
    type: "TINYINT",
    name: "Product_Type",
    length: 4,
  },
  Tariff_Type: {
    type: "CHAR",
    name: "Tariff_Type",
    length: 11,
  },
  Authentication_ID: {
    type: "VARCHAR",
    name: "Authentication_ID",
    length: 60,
  },
  Contract_ID: {
    type: "VARCHAR",
    name: "Contract_ID",
    length: 60,
  },
  Meter_ID: {
    type: "VARCHAR",
    name: "Meter_ID",
    length: 60,
  },
  OBIS_Code: {
    type: "VARCHAR",
    name: "OBIS_Code",
    length: 60,
  },
  Charge_Point_ID: {
    type: "CHAR",
    name: "Charge_Point_ID",
    length: 32,
  },
  Service_Provider_ID: {
    type: "CHAR",
    name: "Service_Provider_ID",
    length: 60,
  },
  Infra_Provider_ID: {
    type: "CHAR",
    name: "Infra_Provider_ID",
    length: 10,
  },
  Calculated_Cost: {
    type: "FLOAT",
    name: "Calculated_Cost",
  },
  Timezone: {
    type: "CHAR",
    name: "Timezone",
    length: 60,
  },
  LocalStart_datetime: {
    type: "DATETIME",
    name: "LocalStart_datetime",
  },
  LocalEnd_datetime: {
    type: "DATETIME",
    name: "LocalEnd_datetime",
  },
  Location_ID: {
    type: "CHAR",
    name: "Location_ID",
    length: 60,
  },
  OU_Code: {
    type: "CHAR",
    name: "OU_Code",
    length: 60,
  },
  Tariff_Name: {
    type: "CHAR",
    name: "Tariff_Name",
    length: 60,
  },
  Start_Tariff: {
    type: "FLOAT",
    name: "Start_Tariff",
  },
  Tariff_kWh: {
    type: "FLOAT",
    name: "Tariff_kWh",
  },
  Token_OU_Code: {
    type: "VARCHAR",
    name: "Token_OU_Code",
    length: 60,
  },
  Token_OU_Name: {
    type: "VARCHAR",
    name: "Token_OU_Name",
    length: 100,
  },
  OU_Name: {
    type: "CHAR",
    name: "OU_Name",
    length: 100,
  },
  Owner: {
    type: "CHAR",
    name: "Owner",
    length: 100,
  },
  Operator: {
    type: "CHAR",
    name: "Operator",
    length: 100,
  },
  Sub_Operator: {
    type: "CHAR",
    name: "Sub_Operator",
    length: 100,
  },
  MeterStart: {
    type: "INT",
    name: "MeterStart",
    length: 11,
  },
  MeterStop: {
    type: "INT",
    name: "MeterStop",
    length: 11,
  },
  ExternalReference: {
    type: "VARCHAR",
    name: "ExternalReference",
    length: 100,
  },
  Charging_Time_Cost: {
    type: "FLOAT",
    name: "Charging_Time_Cost",
  },

  Parking_Time_Cost: {
    type: "FLOAT",
    name: "Parking_Time_Cost",
  },
  ConnectorId: {
    type: "INT",
    name: "ConnectorId",
  },
  EnergyCosts: {
    type: "FLOAT",
    name: "EnergyCosts",
  },
  TransactionId: {
    type: "VARCHAR",
    name: "TransactionId",
    length: 100,
  },
};

function createCdrObject(cdrRawData: string[]): Cdr {
  if (cdrRawData.length > 41) {
    throw Error("");
  }

  return {
    CDR_ID: cdrRawData[0],
    Start_datetime: cdrRawData[1],
    End_datetime: cdrRawData[2],
    Duration: cdrRawData[3],
    Volume: cdrRawData[4],
    Charge_Point_Address: cdrRawData[5],
    Charge_Point_ZIP: cdrRawData[6],
    Charge_Point_City: cdrRawData[7],
    Charge_Point_Country: cdrRawData[8],
    Charge_Point_Type: cdrRawData[9],
    Product_Type: cdrRawData[10],
    Tariff_Type: cdrRawData[11],
    Authentication_ID: cdrRawData[12],
    Contract_ID: cdrRawData[13],
    Meter_ID: cdrRawData[14],
    OBIS_Code: cdrRawData[15],
    Charge_Point_ID: cdrRawData[16],
    Service_Provider_ID: cdrRawData[17],
    Infra_Provider_ID: cdrRawData[18],
    Calculated_Cost: cdrRawData[19],
    Timezone: cdrRawData[20],
    LocalStart_datetime: cdrRawData[21],
    LocalEnd_datetime: cdrRawData[22],
    Location_ID: cdrRawData[23],
    OU_Code: cdrRawData[24],
    Tariff_Name: cdrRawData[25],
    Start_Tariff: cdrRawData[26],
    Tariff_kWh: cdrRawData[27],
    Token_OU_Code: cdrRawData[28],
    Token_OU_Name: cdrRawData[29],
    OU_Name: cdrRawData[30],
    Owner: cdrRawData[31],
    Operator: cdrRawData[32],
    Sub_Operator: cdrRawData[33],
    MeterStart: cdrRawData[34],
    MeterStop: cdrRawData[35],
    ExternalReference: cdrRawData[36],
    Charging_Time_Cost: cdrRawData[37],
    Parking_Time_Cost: cdrRawData[38],
    ConnectorId: cdrRawData[39],
    EnergyCosts: cdrRawData[40],
    TransactionId: cdrRawData[41],
  } as unknown as Cdr;
}

const checkCdrTypesWithDbTargetTypes = (header: any, cdr: any) => {
  if (header.length != cdr.length) {
    throw new Error(
      `Amount of Header field ${header.length} dont match the amount of Data ${cdr.length}`,
    );
  }
  const checkedCdr = [];

  for (let i = 0; i < cdr.length; i++) {
    const field = header[i];
    const mapping = longshipDbMapping[field];

    const data = cdr[i];

    switch (mapping?.type) {
      case "VARCHAR": {
        const isString = typeof data === "string";
        if (!isString || !mapping?.length || data.length > mapping?.length) {
          throw new Error(`DB Type is not VARCHAR Data: ${data}`);
        }
        checkedCdr.push(data);
        break;
      }
      case "CHAR": {
        const isString = typeof data === "string";
        if (!isString || !mapping?.length || data.length > mapping?.length) {
          throw new Error(`DB Type is not CHAR Data: ${data}`);
        }
        checkedCdr.push(data);
        break;
      }
      case "INT": {
        const value = parseInt(data);
        const isInt = Number.isInteger(value);
        if (!isInt) {
          throw new Error(`DB Type is not INT Data: ${data}`);
        }
        checkedCdr.push(value);
        break;
      }
      case "FLOAT": {
        if (/^[-+]?[0-9]*([.,]?[0-9]+)?$/.test(data)) {
          const value = parseFloat(data.replace(",", "."));
          checkedCdr.push(value);
        } else {
          throw new Error(`DB Type is not FLOAT Data: ${data}`);
        }
        break;
      }
      case "DATETIME": {
        const value = new Date(Date.parse(data));
        if (!value || (value < new Date("1/1/2022") && data != "0001-01-01T00:00:00+00:00")) {
          throw new Error(`DB Type is no DATETIME or to low for a valid Date: ${data}`);
        }
        checkedCdr.push(value);
        break;
      }
      case "TINYINT": {
        const value = parseInt(data);
        const isTinyInt = Number.isInteger(value) && value >= -128 && value <= 127;
        if (!isTinyInt) {
          throw new Error(`DB Type is not TINYINT Data: ${data}`);
        }
        checkedCdr.push(value);
        break;
      }
      default: {
        checkedCdr.push(field);
        Logger(
          `check db type for field ${field} failed, because ${mapping?.type} not found`,
          "Import Error",
          "Sync CDRs",
          LogType.INFO,
        );
      }
    }
  }
  return checkedCdr;
};

const inserCdrIntoDB = async (cdr: Cdr[]) => {
  return await prisma.cdr.createMany({
    data: cdr,
    skipDuplicates: true,
  });
};

const createCdr = (header: string[], rawCdrObject: string[]): any => {
  const checkedCdrData = checkCdrTypesWithDbTargetTypes(header, rawCdrObject);

  return {
    CDR_ID: checkedCdrData[0],
    Start_datetime: checkedCdrData[1],
    End_datetime: checkedCdrData[2],
    Duration: checkedCdrData[3],
    DurationInSec: durationToSeconds(checkedCdrData[3] as unknown as string),
    Volume: checkedCdrData[4],
    Charge_Point_Address: checkedCdrData[5],
    Charge_Point_ZIP: checkedCdrData[6],
    Charge_Point_City: checkedCdrData[7],
    Charge_Point_Country: checkedCdrData[8],
    Charge_Point_Type: checkedCdrData[9],
    Product_Type: checkedCdrData[10],
    Tariff_Type: checkedCdrData[11],
    Authentication_ID: checkedCdrData[12],
    Contract_ID: checkedCdrData[13],
    Meter_ID: checkedCdrData[14],
    OBIS_Code: checkedCdrData[15],
    Charge_Point_ID: checkedCdrData[16],
    Service_Provider_ID: checkedCdrData[17],
    Infra_Provider_ID: checkedCdrData[18],
    Calculated_Cost: checkedCdrData[19],
    Timezone: checkedCdrData[20],
    LocalStart_datetime: checkedCdrData[21],
    LocalEnd_datetime: checkedCdrData[22],
    Location_ID: checkedCdrData[23],
    OU_Code: checkedCdrData[24],
    Tariff_Name: checkedCdrData[25],
    Start_Tariff: checkedCdrData[26],
    Tariff_kWh: checkedCdrData[27],
    Token_OU_Code: checkedCdrData[28],
    Token_OU_Name: checkedCdrData[29],
    OU_Name: checkedCdrData[30],
    Owner: checkedCdrData[31],
    Operator: checkedCdrData[32],
    Sub_Operator: checkedCdrData[33],
    MeterStart: checkedCdrData[34],
    MeterStop: checkedCdrData[35],
    ExternalReference: checkedCdrData[36],
    Charging_Time_Cost: checkedCdrData[37],
    Parking_Time_Cost: checkedCdrData[38],
    ConnectorId: checkedCdrData[39],
    EnergyCosts: checkedCdrData[40],
    transactionId: checkedCdrData[41],
  } as unknown as Cdr;
};

export const importCdr = async (
  copy_mode: COPY_MODE = COPY_MODE.DELTA,
  numDays = 10,
): Promise<ApiResponse<ImportStats>> => {
  const headers = LongshipHeaders({ runningOnly: "1" });

  let from = new Date();
  if (copy_mode == COPY_MODE.FULL) {
    from = new Date(2022, 7, 1, 0, 0, 0);
  } else {
    from.setDate(from.getDate() - numDays);
  }

  const to = new Date();
  to.setDate(to.getDate() + 1);

  const url_params = new URLSearchParams({
    from: from.toISOString(),
    to: to.toISOString(),
  }).toString();

  const url = `${env.LONGSHIP_API}cdrs/full-download?${url_params}`;
  const full_cdr = await fetch(url, { method: "GET", headers: headers });
  const response = await full_cdr.arrayBuffer();

  const zip = new AdmZip(toBuffer(response));
  const zipEntries = zip.getEntries();

  if (env.LONGSHIP_EXPORT_FOLDER && !fs.existsSync(env.LONGSHIP_EXPORT_FOLDER)) {
    fs.mkdirSync(env.LONGSHIP_EXPORT_FOLDER);
  }

  zip.extractAllTo(`${env.LONGSHIP_EXPORT_FOLDER}`, true);

  const importStats: ImportStats = {
    totalProcessed: 0,
    successfullyImported: 0,
    errors: [],
  };

  for (const zipEntry of zipEntries) {
    const csv_path = `${env.LONGSHIP_EXPORT_FOLDER}${zipEntry.entryName}`;
    let data = fs.readFileSync(csv_path, "utf8");
    data = data.replace(/^\uFEFF/, "");
    const lines = data.split("\r\n");

    if (lines.length === 0) {
      // Log the error or handle the empty file scenario
      Logger(
        "Empty or corrupt file detected.",
        "CDR Import Error",
        "import cdr from longship",
        LogType.ERROR,
      );
      continue;
    }

    const header = lines[0]?.split(";") || [];
    const rowData = lines.slice(1);

    const cdrs = [];

    for (const rowCDR of rowData) {
      const rawCdrObject = rowCDR.split(";");
      try {
        const cdr: Cdr = createCdr(header, rawCdrObject);
        cdrs.push(cdr);
        importStats.successfullyImported++;
      } catch (e: any) {
        importStats.errors.push(e.message);
      } finally {
        importStats.totalProcessed++;
      }
    }
    const cdrIdsFromImport = cdrs.map((cdr) => cdr.CDR_ID);

    const cdrsFromDb = await prisma.cdr.findMany({
      where: { CDR_ID: { in: cdrIdsFromImport } },
      select: { CDR_ID: true },
    });

    const cdrIfdFromDb = cdrsFromDb.map((entry: { CDR_ID: string }) => entry.CDR_ID);
    const filteredCdrs = cdrs.filter((cdr) => cdr?.CDR_ID && !cdrIfdFromDb.includes(cdr?.CDR_ID));
    let calcCdrs = await computeRoamingCostByEmpPrice(filteredCdrs);
    calcCdrs = await computeAdhocCosts(calcCdrs);
    calcCdrs = await computeDirectPaymentCosts(calcCdrs);
    calcCdrs = await computeCardHolderCosts(calcCdrs);
    await inserCdrIntoDB(calcCdrs);

    const negativeCdrs = calcCdrs.filter((cdr) => (cdr?.Volume ? cdr.Volume < 0 : false));
    const cdrLinks = negativeCdrs
      .filter((cdr) => (cdr?.Volume ? cdr.Volume < 0 : false))
      .map((cdr) => `${env.LONGSHIP_PORTAL_URL}/cdrs/${cdr.CDR_ID}`)
      .join(", ");

    if (negativeCdrs.length > 0)
      await createSystemNotificationForAdmins({
        nachricht: `${cdrLinks.length} negative CDRs wurden importiert. Links zum Longship Portal: ${cdrLinks}`,
        type: NotificationType.WARNING,
      });
  }

  Logger(
    `${importStats.successfullyImported} of ${importStats.totalProcessed} CDRs were successfully imported with ${importStats.errors.length} errors.`,
    "CDR Import Summary",
    "import cdr from longship",
    LogType.INFO,
  );

  return {
    status: importStats.errors.length === 0 ? "success" : "error",
    payload: importStats,
    message: `Imported ${importStats.successfullyImported} of ${importStats.totalProcessed} CDRs.`,
    errorCode: importStats.errors.length > 0 ? "SOME_ERRORS_OCCURRED" : undefined,
    errorDetails: importStats.errors,
  };
};

export default importCdr;
