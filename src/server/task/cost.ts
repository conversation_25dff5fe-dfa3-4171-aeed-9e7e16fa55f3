import type { Cdr, EnergyStockPrice, CdrCost, PowerContract } from "@prisma/client";
import prisma from "../db/prisma";
import type { Prisma } from "@prisma/client";
import { LongshipHeaders } from "~/utils/longship";
import { LogType } from "@prisma/client";
import { findMinStartDatetime } from "~/utils/invoice/invoiceHelper";
import Logger from "../logger/logger";
import { env } from "../../env.js";
import { TypeOfContract } from "~/server/types/types";

export type LocationWithIncludes = Prisma.LocationGetPayload<{
  include: {
    powerContract: {
      select: {
        id: true,
        typeOfContract: true,
        start: true,
        end: true,
        monthlyFixCost: true,
        baseKWhPrice: true,
        kwhPrice: true,
        contractWith: true,
        contractNumber: true,
        customerNumber: true,
        supplyNetworkOperator: true,
        locationId: true,
      },
    };
  };
}>;

interface ChargingPeriods {
  timestamp: Date;
  deltaKwh: number;
  absoluteKwh: number;
  price: number;
}

export enum SupplyNetworkOperator {
  wesernetz = "Wesernetz",
  staedtische_werke = "Städtische Werke Netz + Service (Nordhessen)",
  hoba = "hoba",
  midorion = "midorion",
}

/**
 * Calculates the network fees and taxes based on the contract's baseKWhPrice
 * For dynamic contracts, this replaces the old supplyNetworkOperator calculation
 * @param contract
 * @param cdr
 */
const getEnergyTaxesAndFees = (contract: PowerContract, cdr: Cdr): number => {
  // If baseKWhPrice is set, use it as the network fee (for dynamic contracts)
  if (contract.baseKWhPrice !== null && contract.baseKWhPrice !== undefined) {
    return contract.baseKWhPrice;
  }

  // Fallback to old supplyNetworkOperator logic for existing contracts
  switch (contract.supplyNetworkOperator) {
    case SupplyNetworkOperator.midorion:
      switch (cdr.Start_datetime.getFullYear()) {
        case 2024:
          return 0.16043;
        case 2025:
          return 0.16043;
      }

    case SupplyNetworkOperator.wesernetz:
      switch (cdr.Start_datetime.getFullYear()) {
        case 2022:
          return 0.13;
        case 2023:
          return 0.145;
        case 2024:
          return 0.16;
        case 2025:
          return 0.15641;
        default:
          throw new Error(
            `missing vat price for ${
              SupplyNetworkOperator.wesernetz
            } in ${cdr.Start_datetime.getFullYear()}`,
          );
      }
    case SupplyNetworkOperator.staedtische_werke:
      switch (cdr.Start_datetime.getFullYear()) {
        case 2024:
          return 0.05; // Mittelspannung
        case 2025:
          return 0.05; // Mittelspannung
        default:
          throw new Error(
            `missing vat price for ${
              SupplyNetworkOperator.staedtische_werke
            } in ${cdr.Start_datetime.getFullYear()}`,
          );
      }
    case SupplyNetworkOperator.hoba:
      switch (cdr.Start_datetime.getFullYear()) {
        case 2025:
          return 0.05626;
        default:
          return 0.05626;
      }
    default:
      throw new Error(
        `missing vat price for ${SupplyNetworkOperator.wesernetz} or  ${
          SupplyNetworkOperator.staedtische_werke
        } in ${cdr.Start_datetime.getFullYear()}`,
      );
  }
};
/**
 * Calculated the costs per CDR we need to pay to the energy provider like Voltego
 * @param cdrs
 * @param locations
 * @param stockPrice
 */
const calculateEnergyCost = async (
  cdrs: Cdr[],
  locations: LocationWithIncludes[],
  stockPrice: EnergyStockPrice[],
) => {
  const cost: CdrCost[] = [];

  for (const cdr of cdrs) {
    const location = locations.find((s) => s.id === cdr.Location_ID);
    if (!location) {
      Logger(`no location found for ${cdr.CDR_ID}`, "missing location", "cost", LogType.INFO);
      continue;
    }

    const contract = location.powerContract.find((powerContract) => {
      return powerContract.start < cdr.End_datetime && powerContract.end > cdr.End_datetime;
    });

    if (!contract) {
      Logger(
        `No Power Contract found for ${location.name} CDR:  ${cdr.CDR_ID}`,
        "missing powercontact",
        "cost",
        LogType.INFO,
      );
      continue;
    }

    if (contract?.typeOfContract == TypeOfContract.fixed) {
      if (contract?.kwhPrice != null) {
        cost.push({
          cdrId: cdr.CDR_ID,
          cost: contract.kwhPrice * (cdr?.Volume || 0),
        });
      }
    } else {
      const roundedStartTime = new Date(
        cdr.Start_datetime.getFullYear(),
        cdr.Start_datetime.getMonth(),
        cdr.Start_datetime.getDate(),
        cdr.Start_datetime.getHours(),
      );
      const roundedEndTime = new Date(
        cdr.End_datetime.getFullYear(),
        cdr.End_datetime.getMonth(),
        cdr.End_datetime.getDate(),
        cdr.End_datetime.getHours(),
      );
      const prices = stockPrice.filter((x) => {
        return x.timestamp >= roundedStartTime && x.timestamp <= roundedEndTime;
      });
      if (!prices) {
        Logger(
          `no prices found for CDR:  ${cdr.CDR_ID}`,
          "no energy price for cdr",
          "cost",
          LogType.INFO,
        );
        continue;
      }

      const headers = LongshipHeaders({});
      const url = `${env.LONGSHIP_API}sessions/${cdr.CDR_ID}`;
      // ToDo find a way to request more session at once
      const response = await fetch(url, {
        method: "GET",
        headers: headers,
      });
      if (!response.ok || response.status != 200) {
        Logger(
          `can't fetch session data from Longship for:  ${cdr.CDR_ID}. An error has occured: ${response.status}`,
          "no energy price for cdr",
          "cost",
          LogType.INFO,
        );
        continue;
      }
      let session;
      try {
        session = await response.json();
      } catch (e) {
        Logger(
          `can't fetch session data from Longship for:  ${cdr.CDR_ID}`,
          "no energy price for cdr",
          "cost",
          LogType.INFO,
        );
        continue;
      }

      const sessionCost = session.chargingPeriods.reduce(
        (accu: number, current: ChargingPeriods) => {
          const endTime = new Date(current.timestamp);

          const price = prices.find(
            (price) =>
              price.day == endTime.getDate() &&
              price.hour == endTime.getHours() &&
              price.year == endTime.getFullYear(),
          );
          if (!price) {
            const oldestPrice = stockPrice.reduce((a, b) => {
              return a.timestamp > b.timestamp ? a : b;
            });
            Logger(
              `no stock prices found for CDR:  ${cdr.CDR_ID} for timestamp: ${current.timestamp}. there was ${stockPrice.length} prices found. Olderst price is from ${oldestPrice.timestamp}`,
              "no energy price for cdr",
              "cost",
              LogType.INFO,
            );
            return accu;
          }

          if (current.deltaKwh > 0) {
            const vat = getEnergyTaxesAndFees(contract, cdr);

            return accu + (price.amount / 100) * current.deltaKwh + vat * current.deltaKwh;
          }

          return accu;
        },
        0,
      );
      if (cdr.Volume && cdr.Volume > 0.2 && sessionCost == 0) {
        Logger(
          `No Energyprice found for  ${cdr.CDR_ID} with ${cdr.Volume} kWh`,
          "No energy price found",
          "cost",
          LogType.INFO,
        );
      } else {
        cost.push({
          cdrId: cdr.CDR_ID,
          cost: sessionCost,
        });
      }
    }
  }
  return cost;
};

export const ComputeEnergyCostForCdrs = async (cdrs: Cdr[] = []) => {
  if (cdrs.length == 0) {
    cdrs = await prisma.cdr.findMany({
      where: {
        cost: null,
        End_datetime: {
          gt: new Date("09.14.2022"),
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
  }

  const locations: LocationWithIncludes[] = await prisma.location.findMany({
    include: {
      powerContract: {
        select: {
          id: true,
          typeOfContract: true,
          start: true,
          end: true,
          monthlyFixCost: true,
          baseKWhPrice: true,
          kwhPrice: true,
          contractWith: true,
          contractNumber: true,
          customerNumber: true,
          supplyNetworkOperator: true,
          locationId: true,
        },
      },
    },
  });

  if (!Array.isArray(cdrs) || cdrs.length === 0) {
    return false;
  }

  if (!cdrs[0]?.End_datetime || !cdrs[cdrs.length - 1]?.Start_datetime) {
    return false;
  }

  const minDate = findMinStartDatetime(cdrs);
  //const maxDate = findMaxEndDatetime(cdrs);

  const stockPrice = await prisma.energyStockPrice.findMany({
    where: {
      AND: [
        {
          timestamp: {
            gte: minDate,
          },
        },
        {
          timestamp: {
            lte: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days in the future
          },
        },
      ],
    },
  });

  const cdrCost = await calculateEnergyCost(cdrs, locations, stockPrice);

  await prisma.cdrCost.createMany({
    data: cdrCost,
    skipDuplicates: true,
  });

  const missingIds = [];
  for (const item of cdrs) {
    if (!cdrCost.find((x) => x.cdrId === item.CDR_ID)) {
      missingIds.push(item.CDR_ID);
    }
  }
  if (missingIds.length > 0) {
    Logger(
      `Calc energy cost not successfull for the following CDR ${missingIds.join(", ")}`,
      "calc energy cost fail",
      "energyCost",
      LogType.INFO,
    );
  }

  return true;
};
