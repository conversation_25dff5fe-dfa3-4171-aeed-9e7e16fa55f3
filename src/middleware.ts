import { withAuth } from "next-auth/middleware";
import { validateToken } from "~/utils/ocpiAuth/ocpiAuthUtil";
import { Role } from "@prisma/client";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    const path = req.nextUrl.pathname;
    if (req?.nextauth?.token?.role !== Role.ADMIN) {
      if (
        path.startsWith("/invoice") ||
        path.startsWith("/credit") ||
        path.startsWith("/debug") ||
        path.startsWith("/admin") ||
        path.startsWith("/stripe") ||
        path.startsWith("/cpoContract") ||
        path.startsWith("/cdr") ||
        path.startsWith("/contact") ||
        path.startsWith("/tarif/credit") ||
        path.startsWith("/tarif/mapping") ||
        path.startsWith("/tarif/roaming") ||
        path.startsWith("/tarif/company/update") ||
        path.startsWith("/history") ||
        path.startsWith("/log/") || //log needs / at end. if not /logo would be blocked
        path.startsWith("/powerContract") ||
        path.startsWith("/qonto-transactions") ||
        path.startsWith("/tenantconfiguration") ||
        path.startsWith("/cpo-revenue-dashboard") ||
        path.startsWith("/finance-dashboard") ||
        path.startsWith("/invoice-overview")
      ) {
        return new NextResponse("Not allowed", { status: 401 });
      }
    }
  },
  {
    callbacks: {
      authorized: ({ req, token }) => {
        const path = req.nextUrl.pathname;
        // Wenn es sich um die a/signup Seite handelt, erlaube den Zugriff
        if (
          path.startsWith("/signup") ||
          path.startsWith("/register") ||
          path.startsWith("/login") ||
          path.startsWith("/passwortReset") ||
          path.startsWith("/api/user/verify-invite") ||
          path.startsWith("/api/user/verify-email") ||
          path.startsWith("/api/emp/verify-card") ||
          path.startsWith("/api/user/createByCard") ||
          path.startsWith("/api/user/createUserMinimal") ||
          path.startsWith("/api/cron") ||
          path.startsWith("/api/cronjobs") ||
          path.startsWith("/api/stripeWebhook") ||
          path.startsWith("/api/reset-password") ||
          path.startsWith("/api/stripe/setupCompanySepaDebit") ||
          path.startsWith("/api/stripe/setDefaultCompanyPaymentMethod") ||
          path.startsWith("/api/forecast/") ||
          path.startsWith("/api/v1/") ||
          path.startsWith("/createCompanySepa") ||
          path.startsWith("/logo/")
        )
          return true;

        if (path.startsWith("/ocpi/credentials") || path.startsWith("/ocpi/versions")) {
          return true;
        }
        if (path.startsWith("/ocpi")) {
          return validateToken(req);
        }

        if (path.startsWith("/") && token === null) {
          return false;
        }

        // In allen anderen Fällen, erlaube den Zugriff
        return !!token;
      },
    },
  },
);
