import { type NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import crypto from "crypto";

/**
 * Validates an API key from the request headers
 * @param request - The NextRequest object
 * @returns Promise<{ isValid: boolean; contact?: Contact; error?: string }>
 */
export const validateApiKey = async (
  request: NextRequest,
): Promise<{ isValid: boolean; contact?: any; error?: string }> => {
  try {
    // Get API key from Authorization header
    let authHeader = request.headers.get("authorization");
    if (!authHeader) {
      authHeader = request.headers.get("Authorization");
    }
    if (!authHeader) {
      authHeader = request.headers.get("AUTHORIZATION");
    }

    if (!authHeader) {
      return { isValid: false, error: "No authorization header provided" };
    }

    // Extract API key (expecting format: "Bearer <api-key>" or "ApiKey <api-key>")
    let apiKey: string;
    if (authHeader.startsWith("Bearer ")) {
      apiKey = authHeader.replace("Bearer ", "");
    } else if (authHeader.startsWith("ApiKey ")) {
      apiKey = authHeader.replace("ApiKey ", "");
    } else {
      // Try to use the header value directly
      apiKey = authHeader;
    }

    if (!apiKey || apiKey.trim() === "") {
      return { isValid: false, error: "Invalid API key format" };
    }

    // Find contact with this API key
    const contact = await prisma.contact.findFirst({
      where: {
        apiKey: apiKey.trim(),
      },
      include: {
        ou: true,
        providers: true,
      },
    });

    if (!contact) {
      return { isValid: false, error: "Invalid API key" };
    }

    return { isValid: true, contact };
  } catch (error) {
    console.error("Error validating API key:", error);
    return { isValid: false, error: "Internal server error during API key validation" };
  }
};

/**
 * Generates a new API key
 * @param prefix - Optional prefix for the API key (default: "EUL")
 * @param length - Length of the random part (default: 32)
 * @returns string - The generated API key
 */
export const generateApiKey = (prefix: string = "EUL", length: number = 32): string => {
  const randomPart = crypto
    .randomBytes(Math.ceil(length / 2))
    .toString("hex")
    .slice(0, length);

  return `${prefix}_${randomPart}`;
};

/**
 * Checks if an API key exists in the database
 * @param apiKey - The API key to check
 * @returns Promise<boolean> - True if the key exists
 */
export const apiKeyExists = async (apiKey: string): Promise<boolean> => {
  try {
    const contact = await prisma.contact.findFirst({
      where: {
        apiKey: apiKey,
      },
    });
    return !!contact;
  } catch (error) {
    console.error("Error checking API key existence:", error);
    return false;
  }
};

/**
 * Generates a unique API key that doesn't exist in the database
 * @param prefix - Optional prefix for the API key (default: "EUL")
 * @param length - Length of the random part (default: 32)
 * @param maxAttempts - Maximum attempts to generate a unique key (default: 10)
 * @returns Promise<string> - The generated unique API key
 */
export const generateUniqueApiKey = async (
  prefix: string = "EUL",
  length: number = 32,
  maxAttempts: number = 10,
): Promise<string> => {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const apiKey = generateApiKey(prefix, length);
    const exists = await apiKeyExists(apiKey);

    if (!exists) {
      return apiKey;
    }
  }

  throw new Error("Failed to generate unique API key after maximum attempts");
};
