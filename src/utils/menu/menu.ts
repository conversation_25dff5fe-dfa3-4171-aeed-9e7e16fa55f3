import { MenuItem, MenuType, CollapsibleMenuSection } from "~/utils/menu/menuDef";

export const findMenuItemByPathname = (pathname: string, visibleMenuItems: MenuItem[]): MenuItem | undefined => {
  // Hilfsfunktion zum rekursiven Durchsuchen von einklappbaren Bereichen
  const searchInCollapsibleSection = (section: CollapsibleMenuSection): MenuItem | undefined => {
    for (const item of section.items) {
      if (item.type === MenuType.collapsibleSection) {
        const found = searchInCollapsibleSection(item as CollapsibleMenuSection);
        if (found) return found;
      } else if (item.type === MenuType.menu && item.href === pathname) {
        return item;
      } else if (item.type === MenuType.menu && pathname.startsWith(item.href || "")) {
        return item;
      }
    }
    return undefined;
  };

  // Zuerst nach exakten Übereinstimmungen suchen
  let menuEntry = visibleMenuItems?.find((entry) => {
    if (entry.type === MenuType.collapsibleSection) {
      return searchInCollapsibleSection(entry as CollapsibleMenuSection);
    }
    return entry.type === MenuType.menu && entry.href === pathname;
  });

  // Falls keine exakte Übereinstimmung gefunden wurde, nach Präfixen suchen
  if (!menuEntry) {
    for (const item of visibleMenuItems) {
      if (item.type === MenuType.collapsibleSection) {
        const found = searchInCollapsibleSection(item as CollapsibleMenuSection);
        if (found) {
          if (!menuEntry || (found.type === MenuType.menu && found.href && found.href.length > (menuEntry.type === MenuType.menu ? menuEntry.href?.length || 0 : 0))) {
            menuEntry = found;
          }
        }
      } else if (item.type === MenuType.menu && pathname.startsWith(item.href || "")) {
        // Prüfen, ob das aktuelle href des Items länger ist als das bisher gefundene,
        // um den besten Treffer zu erhalten
        if (!menuEntry || (item.href && item.href.length > (menuEntry.type === MenuType.menu ? menuEntry.href?.length || 0 : 0))) {
          menuEntry = item;
        }
      }
    }
  }
  return menuEntry;
};
