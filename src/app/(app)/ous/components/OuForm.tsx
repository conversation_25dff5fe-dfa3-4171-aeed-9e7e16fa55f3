"use client";

import { useForm } from "react-hook-form";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface FormInputs {
  operatorId?: string;
}

interface OuData {
  id: string;
  name: string;
  code: string;
  operatorId?: string | null;
  externalReference?: string | null;
  customerReference?: string | null;
  address?: string | null;
  city?: string | null;
  postalCode?: string | null;
  country?: string | null;
  companyEmail?: string | null;
  primaryContactperson?: string | null;
  primaryContactpersonEmail?: string | null;
  hotlinePhoneNumber?: string | null;
}

interface Props {
  ouData: OuData;
}

const OuForm = ({ ouData }: Props) => {
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty },
    reset,
  } = useForm<FormInputs>({
    defaultValues: {
      operatorId: ouData.operatorId || "",
    },
  });

  const onSubmit = async (data: FormInputs) => {
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const response = await fetch(`/api/ou/${ouData.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operatorId: data.operatorId?.trim() || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessage(errorData.error || "Ein Fehler ist aufgetreten");
        return;
      }

      const updatedOu = await response.json();
      setSuccessMessage("OperatorId erfolgreich aktualisiert!");
      
      // Reset form with new data
      reset({ operatorId: updatedOu.operatorId || "" });
      
      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 1500);

    } catch (error) {
      console.error("Error updating OU:", error);
      setErrorMessage("Ein unerwarteter Fehler ist aufgetreten");
    }
  };

  return (
    <div className="max-w-2xl">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* OU Information (Read-only) */}
        <div className="bg-gray-50 p-4 rounded-md dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Organisationseinheit Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Name:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{ouData.name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Code:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{ouData.code}</span>
            </div>
            {ouData.externalReference && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Externe Referenz:</span>
                <span className="ml-2 text-gray-900 dark:text-white">{ouData.externalReference}</span>
              </div>
            )}
            {ouData.customerReference && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Kunden Referenz:</span>
                <span className="ml-2 text-gray-900 dark:text-white">{ouData.customerReference}</span>
              </div>
            )}
          </div>
        </div>

        {/* OperatorId Field */}
        <div>
          <label htmlFor="operatorId" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Operator ID
          </label>
          <div className="mt-1">
            <input
              type="text"
              id="operatorId"
              maxLength={5}
              {...register("operatorId", { 
                maxLength: { 
                  value: 5, 
                  message: "OperatorId darf maximal 5 Zeichen haben" 
                },
                pattern: {
                  value: /^[A-Za-z0-9]*$/,
                  message: "OperatorId darf nur Buchstaben und Zahlen enthalten"
                }
              })}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="z.B. OP001"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Maximal 5 Zeichen (Buchstaben und Zahlen)
            </p>
          </div>
          {errors.operatorId && (
            <p className="mt-1 text-sm text-red-600">{errors.operatorId.message}</p>
          )}
        </div>

        {/* Success/Error Messages */}
        {successMessage && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-800">{successMessage}</p>
          </div>
        )}

        {errorMessage && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{errorMessage}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            onClick={() => router.back()}
            className="bg-gray-300 text-gray-700 hover:bg-gray-400"
          >
            Abbrechen
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !isDirty}
            className="bg-primary text-white hover:bg-primary-dark disabled:opacity-50"
          >
            {isSubmitting ? (
              <>
                <FiLoader className="animate-spin mr-2" />
                Speichern...
              </>
            ) : (
              "Speichern"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default OuForm;
