import Card from "../../../../../component/card";
import OuForm from "../../components/OuForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect, notFound } from "next/navigation";
import prisma from "~/server/db/prisma";

interface Props {
  params: {
    id: string;
  };
}

const getOu = async (id: string) => {
  try {
    const ou = await prisma.ou.findUnique({
      where: { 
        id,
        deleted: null,
      },
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
        externalReference: true,
        customerReference: true,
        address: true,
        city: true,
        postalCode: true,
        country: true,
        companyEmail: true,
        primaryContactperson: true,
        primaryContactpersonEmail: true,
        hotlinePhoneNumber: true,
      },
    });

    return ou;
  } catch (error) {
    console.error("Error fetching OU:", error);
    return null;
  }
};

const EditOuPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    redirect("/");
  }

  const ou = await getOu(params.id);

  if (!ou) {
    notFound();
  }

  return (
    <Card header_left={`OU bearbeiten: ${ou.name}`}>
      <OuForm ouData={ou} />
    </Card>
  );
};

export default EditOuPage;
