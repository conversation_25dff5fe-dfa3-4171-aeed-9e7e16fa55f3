"use client";

import React, { useEffect, useState } from "react";
import { ColDef } from "ag-grid-community";
import Table from "~/utils/table/table";
import Card from "~/component/card";
import type { ContactInvoiceOverview, InvoiceOverviewData } from "~/app/api/invoice-overview/route";

// Format month for display
const formatMonthName = (monthKey: string): string => {
  const [year, month] = monthKey.split("-");
  const date = new Date(parseInt(year), parseInt(month) - 1, 1);
  return date.toLocaleDateString("de-DE", {
    month: "short",
    year: "numeric",
  });
};

// Get current month key
const getCurrentMonthKey = (): string => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
};

// Cell renderer for invoice/credit counts with chips
const InvoiceCellRenderer = ({ value, data, colDef }: any) => {
  const monthKey = colDef.field?.replace("month_", "");
  const currentMonth = getCurrentMonthKey();
  const isCurrentMonth = monthKey === currentMonth;

  if (!monthKey || !data.monthlyInvoices[monthKey]) {
    return <span className="text-gray-400">-</span>;
  }

  const monthData = data.monthlyInvoices[monthKey];
  const hasInvoice = monthData.hasInvoice;
  const hasCredit = monthData.hasCredit;
  const hasAnyInvoice = hasInvoice || hasCredit;

  // Check if this contact has ever had any invoices (to determine first invoice month)
  const hasEverHadInvoices = Object.values(data.monthlyInvoices).some((month: any) =>
    month.hasInvoice || month.hasCredit
  );

  // Find the first month with any invoice for this contact
  const firstInvoiceMonth = Object.keys(data.monthlyInvoices)
    .sort()
    .find(month => {
      const monthInvoiceData = data.monthlyInvoices[month];
      return monthInvoiceData.hasInvoice || monthInvoiceData.hasCredit;
    });

  // If no invoices ever, or current month is before first invoice month, show empty
  if (!hasEverHadInvoices || (firstInvoiceMonth && monthKey < firstInvoiceMonth)) {
    return <span className="text-gray-400">-</span>;
  }

  // Determine chip color and text based on what exists
  let chipClass = "";
  let chipText = "";

  if (hasInvoice && hasCredit) {
    // Green chip for both INVOICE and CREDIT
    chipClass = "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    chipText = `I:${monthData.invoices} C:${monthData.credits}`;
  } else if (hasInvoice && !hasCredit) {
    // Yellow chip for only INVOICE
    chipClass = "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    chipText = `I:${monthData.invoices}`;
  } else if (!hasInvoice && hasCredit) {
    // Orange chip for only CREDIT
    chipClass = "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    chipText = `C:${monthData.credits}`;
  } else {
    // Red chip for nothing (but only after first invoice month)
    chipClass = "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    chipText = "Keine";
  }

  return (
    <div className="flex justify-center">
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${chipClass}`}>
        {chipText}
      </span>
    </div>
  );
};

const InvoiceOverviewTable = () => {
  const [data, setData] = useState<InvoiceOverviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/invoice-overview");
        if (!response.ok) {
          throw new Error("Failed to fetch data");
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card header_left="Lade Daten...">
        <div className="p-4">Loading...</div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card header_left="Fehler">
        <div className="p-4 text-red-600">Error: {error}</div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card header_left="Keine Daten">
        <div className="p-4">Keine Daten verfügbar</div>
      </Card>
    );
  }

  // Create column definitions
  const createColumnDefs = (months: string[]): ColDef[] => {
    const baseColumns: ColDef[] = [
      {
        headerName: "Contact",
        field: "contactName",
        pinned: "left",
        width: 200,
        cellRenderer: ({ data }: any) => (
          <div>
            <div className="font-medium">{data.contactName}</div>
            {data.companyName && <div className="text-xs text-gray-500">{data.companyName}</div>}
          </div>
        ),
      },
    ];

    const monthColumns: ColDef[] = months.map((month) => ({
      headerName: formatMonthName(month),
      field: `month_${month}`,
      width: 100,
      cellRenderer: InvoiceCellRenderer,
      sortable: false,
    }));

    return [...baseColumns, ...monthColumns];
  };

  const columnDefs = createColumnDefs(data.months);

  return (
    <div className="space-y-6">
      {/* CPO Contacts */}
      <Card header_left={`CPO Contacts (${data.cpoContacts.length})`}>
        <div className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2">
            Grün: I+C
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mr-2">
            Gelb: Nur I
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 mr-2">
            Orange: Nur C
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Rot: Keine
          </span>
        </div>
        <Table
          rowData={data.cpoContacts}
          columnDefs={columnDefs}
          pagination={true}
          paginationPageSize={20}
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
        />
      </Card>

      {/* Non-CPO Contacts */}
      <Card header_left={`Nicht-CPO Contacts (${data.nonCpoContacts.length})`}>
        <div className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2">
            Grün: I+C
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mr-2">
            Gelb: Nur I
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 mr-2">
            Orange: Nur C
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Rot: Keine
          </span>
        </div>
        <Table
          rowData={data.nonCpoContacts}
          columnDefs={columnDefs}
          pagination={true}
          paginationPageSize={20}
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
        />
      </Card>
    </div>
  );
};

export default InvoiceOverviewTable;
