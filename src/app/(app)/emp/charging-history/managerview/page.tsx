import Card from "~/component/card";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import React from "react";
import { ChargingHistoryTable } from "~/app/(app)/emp/charging-history/components/ChargingHistoryTable";
import { Role } from "@prisma/client";
import Headline from "~/component/Headline";
import NotFound from "~/app/(app)/not-found";
import { getOusBelowOu } from "~/server/model/ou/func";

const getCdrsForCardManager = async () => {
  const session = await getServerSession(authOptions);

  const ou = session?.user?.selectedOu;

  if (ou) {
    const ouWithChildren = await getOusBelowOu(ou);
    const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
    return prisma.cdr.findMany({
      where: { OU_Code: { in: ouCodes }, End_datetime: { gt: new Date("01.01.2024") } }, //avoid old CDRs from 'recycled' OUs
      include: { companyTarif: true, tarif: true, cost: true, creditPayout: true },
    });
  }
};

const Page = async () => {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    (session?.user?.role !== Role.CARD_MANAGER &&
      session?.user?.role !== Role.ADMIN &&
      session?.user?.role !== Role.CPO)
  ) {
    return <NotFound />;
  }
  return (
    <>
      <Card className={"mb-4"}>
        <Headline title={"Ladevorgänge"} />
        <div className="mt-3 rounded-lg bg-gray-50 p-4 dark:bg-blue-900/20">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-primary dark:text-blue-200">
                Übersicht der Ladevorgänge
              </h3>
              <div className="mt-2 text-sm text-primary dark:text-blue-300">
                <p>Hier können Sie alle Ladevorgänge an ihrer Infrastruktur einsehen.</p>
              </div>
            </div>
          </div>
        </div>

        <ChargingHistoryTable cdrs={(await getCdrsForCardManager()) ?? []} />
      </Card>
    </>
  );
};

export default Page;
