"use client";
import React, { Suspense, useEffect, useState } from "react";
import { PaymentWrapper } from "~/app/(app)/emp/payment/components/PaymentWrapper";
import Headline from "~/component/Headline";

import { useForm } from "react-hook-form";
import { useSession } from "next-auth/react";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import Loading from "~/app/(app)/loading";
import { CompanyTarif } from "@prisma/client";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { FaCheckCircle } from "react-icons/fa";
import Card from "~/component/card";
import { FaTriangleExclamation } from "react-icons/fa6";

interface FormInputs {
  addressId: string;
  name: string;
  lastName: string;
  street: string;
  streetNr: string;
  city: string;
  zip: string;
  country: string;
  companyName: string;
}
const Page = () => {
  const [clientSecret, setClientSecret] = useState<string>();

  const createPaymentIntent = async () => {
    const result = await fetch("/api/stripe/setupSepaDebit", {
      method: "GET",
    });
    //profil form ausblenden
    setProfileUpdated(true);
    setClientSecret((await result.json()).clientSecret);
  };
  const [tariffs, setTariffs] = useState<CompanyTarif[]>([]);
  const [selectedTariffs, setSelectedTariffs] = useState<string[]>([]);
  const [profileUpdated, setProfileUpdated] = useState<boolean>(false);
  const { data: session, update: updateSession } = useSession();
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting, isDirty, isSubmitSuccessful, isLoading },
  } = useForm<FormInputs>({ defaultValues: { addressId: "" } });

  const fetchTarifs = async () => {
    // Hier die Tarife der Nutzergruppe des Benutzers laden
    const response = await fetch(`/api/tarif/userGroupTarifs`, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    });

    if (response.ok) {
      const tarifs = await response.json();
      setTariffs(tarifs);

      // Auto-select non-optional tariffs
      const nonOptionalTariffs = tarifs.filter((tarif: CompanyTarif) => !tarif.optional);
      setSelectedTariffs(nonOptionalTariffs.map((tarif: CompanyTarif) => tarif.id));
    }
  };
  useEffect(() => {
    void fetchTarifs();
  }, []);

  // AC/DC validation function
  const validateTariffSelection = (newSelectedTariffs: string[]) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const selectedTariffObjects = tariffs.filter((tarif) => newSelectedTariffs.includes(tarif.id));
    const validSelectedTariffs = selectedTariffObjects.filter((tarif) => {
      const validFrom = new Date(tarif.validFrom);
      const validTo = new Date(tarif.validTo);
      return validFrom <= today && validTo >= today;
    });

    const acTariffs = validSelectedTariffs.filter((tarif) => tarif.currentType === "AC");
    const dcTariffs = validSelectedTariffs.filter((tarif) => tarif.currentType === "DC");

    if (acTariffs.length > 1) {
      return { valid: false, error: "Sie können nur einen AC-Tarif auswählen" };
    }

    if (dcTariffs.length > 1) {
      return { valid: false, error: "Sie können nur einen DC-Tarif auswählen" };
    }

    return { valid: true, error: "" };
  };

  // Handle tariff selection
  const handleTariffToggle = (tariffId: string, checked: boolean) => {
    let newSelection: string[];

    if (checked) {
      newSelection = [...selectedTariffs, tariffId];
    } else {
      // Don't allow deselecting non-optional tariffs
      const tariff = tariffs.find((t) => t.id === tariffId);
      if (tariff && !tariff.optional) {
        setErrorMessage("Pflicht-Tarife können nicht abgewählt werden");
        return;
      }
      newSelection = selectedTariffs.filter((id) => id !== tariffId);
    }

    // Validate AC/DC constraints
    const validation = validateTariffSelection(newSelection);
    if (!validation.valid) {
      setErrorMessage(validation.error);
      return;
    }

    setSelectedTariffs(newSelection);
    setErrorMessage("");
  };

  useEffect(() => {
    if (session?.user?.lastName) {
      createPaymentIntent();
    }
  }, [session?.user]);
  const onSubmit = async (data: FormInputs) => {
    try {
      const response = await fetch("/api/user/updateProfile", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data }),
      });
      if (!response.ok) {
        setErrorMessage(await response.text());
        setSuccessMessage("");
      } else {
        //session daten setzen, damit in der nächsten form die eingegebenen Daten direkt verwendet werden
        void updateSession({ name: watch("name"), lastName: watch("lastName") });

        // zeige Sepa Felder
        void createPaymentIntent();

        //verstecke userform
        setProfileUpdated(true);

        //falls vorher ein fehler war, lösche den
        setErrorMessage("");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
        setSuccessMessage("");
      } else {
        setErrorMessage("Fehler beim Speichern");
        setSuccessMessage("");
      }
    }
  };

  return (
    <>
      {!session?.user?.email && <Loading />}

      {!profileUpdated && session?.user?.email && (
        <div className={"w-full lg:w-2/3 xl:w-1/2"}>
          <Headline title={"Schritt 1: Profil vervollständigen"} />
          <form
            onSubmit={handleSubmit(onSubmit)}
            className={`w-full space-y-4 rounded-lg bg-white  p-4 transition-all`}
          >
            <div className="flex flex-col gap-2">
              <div className="">
                <label className="block text-sm font-medium">Email</label>
                <input
                  value={session?.user?.email}
                  disabled={true}
                  readOnly={true}
                  className="disabled bg-eul-lightgray block w-full appearance-none rounded-lg
              border border-solid border-gray-300
              bg-clip-padding px-3 py-2 text-sm font-normal
              leading-5.6 text-gray-700 outline-none transition-all
              ease-soft  placeholder:text-gray-500
              focus:shadow-soft-primary-outline
              focus:outline-none"
                />
              </div>
              <div className={""}>
                <label className="block text-sm font-medium">
                  Firmenname{" "}
                  <span className={"text-xs text-gray-600"}>
                    (wird auf der Rechnung verwendet, wenn angegeben):
                  </span>
                </label>
                <input
                  placeholder={"optional zur Verwendung auf Rechnungen"}
                  {...register("companyName")}
                  className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                />
              </div>

              <>
                <div className={"flex  w-full flex-row gap-2"}>
                  <div className={"w-full "}>
                    <label className="block text-sm font-medium">Vorname*</label>
                    <input
                      {...register("name", { required: "Bitte einen Vornamen eingeben" })}
                      className="block w-full  appearance-none rounded-lg border
              border-solid border-gray-300 bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                    />
                    {errors.name && <p className={"text-red-500"}>{errors.name.message}</p>}
                  </div>
                  <div className="w-full">
                    <label className="block text-sm font-medium">Nachname*</label>
                    <input
                      {...register("lastName", { required: "Bitte einen Nachnamen eingeben" })}
                      className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                    />
                    {errors.lastName && <p className={"text-red-500"}>{errors.lastName.message}</p>}
                  </div>
                </div>

                <>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Land*</label>
                      <select
                        {...register("country", { required: "Bitte ein Land auswählen" })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      >
                        <option value="Deutschland">Deutschland</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">PLZ*</label>
                      <input
                        {...register("zip", { required: "Bitte eine Postleitzahl eingeben" })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                      {errors.zip && <p className={"text-red-500"}>{errors.zip.message}</p>}
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Ort*</label>
                      <input
                        {...register("city", { required: "Bitte eine Stadt eingeben" })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />{" "}
                      {errors.city && <p className={"text-red-500"}>{errors.city.message}</p>}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="space-y-2 md:col-span-2">
                      <label className="block text-sm font-medium">Straße*</label>
                      <input
                        {...register("street", { required: "Bitte eine Straße eingeben" })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                      {errors.street && <p className={"text-red-500"}>{errors.street.message}</p>}
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">Hausnummer*</label>
                      <input
                        {...register("streetNr", { required: "Bitte eine Hausnummer eingeben" })}
                        className="block w-full appearance-none rounded-lg border
              border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
              text-gray-700 outline-none transition-all ease-soft
              placeholder:text-gray-500  focus:shadow-soft-primary-outline
              focus:outline-none dark:bg-gray-950 dark:text-white/80
              dark:placeholder:text-white/80"
                      />
                      {errors.streetNr && (
                        <p className={"text-red-500"}>{errors.streetNr.message}</p>
                      )}
                    </div>
                  </div>
                </>
              </>

              <Button
                disabled={(isSubmitting || isSubmitSuccessful) && !isDirty}
                className={"w-full sm:max-w-64"}
                type="submit"
              >
                Weiter {isSubmitting ? <FiLoader className="animate-spin" /> : ""}
              </Button>
            </div>
          </form>
          {successMessage && <span className={"text-green-600"}>{successMessage}</span>}
          {errorMessage && <span className={"text-red-600"}>{errorMessage}</span>}
        </div>
      )}

      {!clientSecret && profileUpdated && <Loading />}
      {clientSecret && (
        <>
          <div className={"flex min-h-[75vh] flex-col"}>
            <div className={"flex flex-row items-center gap-1"}>
              <span>Profil vollständig</span> <FaCheckCircle size={18} color={"green"} />
            </div>
            <Headline title={"Schritt 2: Lastschriftmandat hinzufügen"} />
            {tariffs?.length == 0 && (
              <div
                className="border-primary relative mb-3  rounded-xl border bg-gray-100 px-4 py-3  text-primary sm:w-70/100"
                role="alert"
              >
                <span>
                  Es wurden leider noch keine Tarife vom Betreiber hinterlegt. Zahlungsmittel kann
                  noch nicht hinterlegt werden.
                </span>
              </div>
            )}
            {tariffs && tariffs?.length > 0 && (
              <>
                {/* Error Message */}
                {errorMessage && (
                  <div className="mb-4 rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-red-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-800 dark:text-red-200">{errorMessage}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className={"flex flex-col"}>
                  <div>
                    Folgende Tarife werden mit der Bestätigung des Lastschriftmandates gebucht:
                  </div>
                  <div className={"mt-3 flex flex-col gap-1 sm:flex-row"}>
                    {tariffs.map((tarif) => (
                      <TarifCard
                        blockingFee={tarif.blockingFee}
                        blockingFeeMax={tarif.blockingFeeMax}
                        blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                        size={"small"}
                        oneTimeFeePayer={tarif.oneTimeFeePayer}
                        vat={19}
                        oneTimeFee={tarif.oneTimeFee}
                        basicFee={tarif.basicFee}
                        currentType={tarif.currentType ?? ""}
                        key={tarif.id}
                        tarifId={tarif.id}
                        interactive={true}
                        optional={tarif.optional}
                        tarifName={tarif.name}
                        pricekWh={tarif.energyPrice}
                        priceSession={tarif.sessionPrice}
                        title={tarif.name ?? ""}
                        description={
                          tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"
                        }
                        internal={tarif.internal}
                        checked={selectedTariffs.includes(tarif.id)}
                        onCheck={(event) => handleTariffToggle(tarif.id, event.target.checked)}
                      />
                    ))}
                  </div>
                </div>

                {selectedTariffs.length > 0 ? (
                  <div className={"flex flex-col gap-5 rounded-xl bg-clip-border p-4 shadow-2xl"}>
                    <span>
                      Zum aktuellen Zeitpunkt unterstützen wir das Hinzufügen eines SEPA
                      Lastschrift-Mandates zur monatlichen Abrechnung. Bitte nutze das folgende
                      Formular, um ein Lastschriftmandat hinzuzufügen, sodass eine Ladekarte
                      bestellt und aktiviert werden kann.
                    </span>

                    <PaymentWrapper
                      clientSecret={clientSecret ?? ""}
                      successUrl={"/emp/card"}
                      selectedTariffIds={selectedTariffs}
                    />
                  </div>
                ) : (
                  <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900/20">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-yellow-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          Keine Tarife ausgewählt
                        </h3>
                        <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                          <p>
                            Bitte wählen Sie mindestens einen Tarif aus, bevor Sie fortfahren
                            können.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default Page;
