import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import ForecastClientWrapper from "./components/ForecastClientWrapper";

export const revalidate = 0;

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return <NotFound />;
  }

  return <ForecastClientWrapper />;
};
export default Page;
