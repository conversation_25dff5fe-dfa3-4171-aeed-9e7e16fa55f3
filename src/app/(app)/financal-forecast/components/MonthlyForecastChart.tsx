"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

interface MonthlyForecastData {
  month: string;
  revenue: number;
  energyGrossMargin: number;
  thgRevenue: number;
  totalGrossMargin: number;
  kWh: number;
  sessions: number;
}

interface MonthlyForecastChartProps {
  monthlyData: MonthlyForecastData[];
}

const MonthlyForecastChart = ({ monthlyData }: MonthlyForecastChartProps) => {
  // Check if there's any meaningful data
  const hasData = monthlyData.some(d =>
    d.revenue > 0 || d.totalGrossMargin > 0 || d.kWh > 0 || d.sessions > 0
  );

  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Calculate trend lines using linear regression
  const calculateTrendLine = (data: number[]) => {
    const n = data.length;
    if (n < 2) return data.map(() => null);

    // Filter out zero values for better trend calculation
    const nonZeroIndices = data.map((val, i) => ({ val, i })).filter(item => item.val > 0);

    if (nonZeroIndices.length < 2) {
      return data.map(() => null);
    }

    const xSum = nonZeroIndices.reduce((sum, item) => sum + item.i, 0);
    const ySum = nonZeroIndices.reduce((sum, item) => sum + item.val, 0);
    const xySum = nonZeroIndices.reduce((sum, item) => sum + item.i * item.val, 0);
    const x2Sum = nonZeroIndices.reduce((sum, item) => sum + item.i * item.i, 0);
    const n2 = nonZeroIndices.length;

    const slope = (n2 * xySum - xSum * ySum) / (n2 * x2Sum - xSum * xSum);
    const intercept = (ySum - slope * xSum) / n2;

    return data.map((_, i) => slope * i + intercept);
  };

  // Filter data to start from the first month with actual data
  const firstDataIndex = monthlyData.findIndex(d =>
    d.revenue > 0 || d.totalGrossMargin > 0 || d.kWh > 0 || d.sessions > 0
  );

  // If no data found, return early with a message
  if (!hasData) {
    return (
      <div className="h-80 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-lg font-medium">Keine Daten verfügbar</p>
          <p className="text-sm">Führen Sie zuerst die monatliche Aggregation aus, um Daten zu generieren.</p>
        </div>
      </div>
    );
  }

  // If no data found, show all months (fallback)
  const filteredData = firstDataIndex >= 0 ? monthlyData.slice(firstDataIndex) : monthlyData;

  // Prepare data for the chart
  const months = filteredData.map(d => d.month);
  const revenueData = filteredData.map(d => d.revenue);
  const marginData = filteredData.map(d => d.totalGrossMargin);

  // Calculate trend lines
  const revenueTrend = calculateTrendLine(revenueData);
  const marginTrend = calculateTrendLine(marginData);

  // Create dynamic title with date range
  const startMonth = filteredData.length > 0 ? formatMonth(filteredData[0].month) : "";
  const endMonth = filteredData.length > 0 ? formatMonth(filteredData[filteredData.length - 1].month) : "";
  const titleText = filteredData.length > 1
    ? `Monatlicher Umsatz und Marge Trend (${startMonth} - ${endMonth})`
    : filteredData.length === 1
    ? `Monatlicher Umsatz und Marge (${startMonth})`
    : `Monatlicher Umsatz und Marge Trend`;

  const options = {
    title: {
      text: titleText,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function(params: any) {
        let result = `<strong>${formatMonth(params[0].axisValue)}</strong><br/>`;
        params.forEach((param: any) => {
          if (param.value !== null && !param.seriesName.includes("Trend")) {
            result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: ["Umsatz", "Gesamtmarge", "Umsatz Trend", "Marge Trend"],
      top: 35,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: months.map(formatMonth),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
      },
    },
    yAxis: {
      type: "value",
      name: "Betrag (€)",
      nameLocation: "middle",
      nameGap: 50,
      axisLabel: {
        formatter: function(value: number) {
          return formatCurrency(value);
        },
        fontSize: 10,
      },
    },
    series: [
      {
        name: "Umsatz",
        type: "line",
        data: revenueData,
        lineStyle: {
          color: "#3b82f6",
          width: 3,
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Gesamtmarge",
        type: "line",
        data: marginData,
        lineStyle: {
          color: "#10b981",
          width: 3,
        },
        itemStyle: {
          color: "#10b981",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Umsatz Trend",
        type: "line",
        data: revenueTrend,
        lineStyle: {
          color: "#3b82f6",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "none",
        symbolSize: 0,
        connectNulls: true,
        smooth: true,
      },
      {
        name: "Marge Trend",
        type: "line",
        data: marginTrend,
        lineStyle: {
          color: "#10b981",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#10b981",
        },
        symbol: "none",
        symbolSize: 0,
        connectNulls: true,
        smooth: true,
      },
    ],
  };

  return (
    <div className="h-80">
      <ReactECharts option={options} style={{ height: "100%", width: "100%" }} />
    </div>
  );
};

export default MonthlyForecastChart;
