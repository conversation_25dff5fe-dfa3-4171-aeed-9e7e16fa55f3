"use client";

import React, { useState, useEffect } from "react";
import Card from "~/component/card";
import MonthlyForecast<PERSON>hart from "./MonthlyForecastChart";
import PowerContractWarning from "./PowerContractWarning";
import { Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle } from "react-icons/fa";
import { userStore } from "~/server/zustand/store";

interface MonthlyForecastData {
  month: string;
  revenue: number;
  energyGrossMargin: number;
  thgRevenue: number;
  totalGrossMargin: number;
  kWh: number;
  sessions: number;
}

const MonthlyForecastWrapper = () => {
  const [monthlyData, setMonthlyData] = useState<MonthlyForecastData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Subscribe to OU changes
  const { selectedOuId, selectedOuName } = userStore();

  useEffect(() => {
    const fetchMonthlyData = async () => {
      try {
        setLoading(true);
        setError(null);

        const url = selectedOuId
          ? `/api/forecast/monthly-data?ouId=${encodeURIComponent(selectedOuId)}`
          : "/api/forecast/monthly-data";
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.status === "success") {
          setMonthlyData(result.data);
        } else {
          throw new Error(result.message || "Failed to fetch data");
        }
      } catch (err) {
        console.error("Error fetching monthly forecast data:", err);
        setError(err instanceof Error ? err.message : "Unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchMonthlyData();
  }, [selectedOuId]); // Re-fetch when OU changes

  if (loading) {
    return (
      <Card className="mt-5">
        <div className="flex items-center justify-center p-8">
          <FaSpinner className="animate-spin mr-2" />
          <span>Lade monatliche Forecast-Daten{selectedOuName && selectedOuName !== "..." ? ` für ${selectedOuName}` : ''}...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mt-5">
        <div className="flex items-center justify-center p-8 text-red-600">
          <FaExclamationTriangle className="mr-2" />
          <span>Fehler beim Laden der Daten: {error}</span>
        </div>
      </Card>
    );
  }



  return (
    <>
      <Card className="mt-5">
        <div className="p-6">
          <MonthlyForecastChart monthlyData={monthlyData} />
        </div>
      </Card>
      <PowerContractWarning />
    </>
  );
};

export default MonthlyForecastWrapper;
