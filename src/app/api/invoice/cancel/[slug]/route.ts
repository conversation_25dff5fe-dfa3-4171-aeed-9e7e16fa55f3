import type { NextRequest } from "next/server";
import prisma from "../../../../../server/db/prisma";
import { NextResponse } from "next/server";
import type { Invoice, InvoicePosition, Prisma } from "@prisma/client";
import { KindOfInvoice, StateOfInvoice } from "@prisma/client";
import { getSha1 } from "~/utils/files/getSha1";
import { createInvoicePDF } from "../../../../../utils/invoice/createInvoicePDF";
import { CREDIT_PREFIX, INVOICE_PREFIX } from "../../../../../utils/invoice/const";
import { env } from "../../../../../env.js";
import { checkAndCreateDir } from "~/utils/invoice/invoiceHelper";

interface Props {
  params: {
    slug: string;
  };
}

export type InvoiceWithIncludes = Prisma.InvoiceGetPayload<{
  include: {
    contact: {
      include: {
        contactAddress: true;
      };
    };
    invoicePositions: {
      include: {
        tarif: true;
      };
    };
    cdrs: {
      include: {
        cost: true;
      };
    };
    creditCdrs: true;
  };
}>;

const createCancelInvoice = async (invoice: InvoiceWithIncludes) => {
  const now = new Date();

  const prefix = invoice.kindOfInvoice == KindOfInvoice.INVOICE ? INVOICE_PREFIX : CREDIT_PREFIX;
  const connectUser = { user: { connect: { id: invoice.userId ?? "" } } };
  const connectContact = {
    contact: {
      connect: { id: invoice?.contact?.id },
    },
  };
  const connectObj = invoice.userId ? connectUser : connectContact;

  const cancelInvoice = await prisma.invoice.create({
    data: {
      invoiceParent: {
        connect: {
          id: invoice.id,
        },
      },
      sumNet: invoice.sumNet * -1,
      sumTax: invoice.sumTax * -1,
      sumGross: invoice.sumGross * -1,
      history: invoice.history,
      sumKwh: invoice.sumKwh,
      sumSession: invoice.sumSession,
      subject: invoice.subject,
      invoiceDate: now,
      createDate: now,
      servicePeriodFrom: invoice.servicePeriodFrom,
      servicePeriodTo: invoice.servicePeriodTo,
      invoiceId: invoice.invoiceId,
      invoiceIdSufix: "S",
      invoiceNumber: `${prefix}${invoice?.invoiceDate?.getFullYear()}-${String(
        invoice.invoiceId,
      ).padStart(5, "0")}-S`,
      kindOfInvoice:
        invoice.kindOfInvoice == KindOfInvoice.CREDIT
          ? KindOfInvoice.CREDIT_STORNO
          : KindOfInvoice.STORNO,
      stateOfInvoice: StateOfInvoice.CREATED,
      sendAsMail: false,
      ...connectObj,
      invoicePositions: {
        create: invoice.invoicePositions.map(
          (invoicePosition): Omit<InvoicePosition, "id" | "invoiceId"> => {
            return {
              pos: invoicePosition.pos,
              description: invoicePosition.description,
              title: invoicePosition.title,
              unit: invoicePosition.unit,
              unitPrice: invoicePosition.unitPrice,
              amount: invoicePosition.amount * -1,
              sumTax: invoicePosition.sumTax * -1,
              sumNet: invoicePosition.sumNet * -1,
              sumGross: invoicePosition.sumGross * -1,
              taxRate: invoicePosition.taxRate,
              tarifId: invoicePosition.tarifId,
            };
          },
        ),
      },
    },
    include: {
      user: { include: { address: true, ou: true } },
      contact: {
        include: {
          providers: true,
          contactAddress: true,
        },
      },
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      cdrs: {
        include: {
          cost: true,
        },
      },
      creditCdrs: {
        include: { cost: true },
      },
      invoiceParent: true,
    },
  });
  let dir = "";
  if (cancelInvoice.contact) {
    const provider_name =
      cancelInvoice?.contact?.name?.replaceAll(" ", "_").toLowerCase() ?? "unknown_contact_name";
    dir = `${env.INVOICE_FOLDER}/${now.getFullYear()}/${provider_name}`;
  } else if (cancelInvoice?.user) {
    const userOuName = cancelInvoice.user.ou.name;
    dir = `${env.INVOICE_FOLDER}/${now.getFullYear()}/${userOuName}`;
  }

  checkAndCreateDir(dir);

  const pdfPath = await createInvoicePDF({ invoice: cancelInvoice, dir });
  const fileName = `${prefix}${cancelInvoice.invoiceNumber}.pdf`;
  await prisma.fileRef.create({
    data: {
      invoice: {
        connect: {
          id: cancelInvoice.id,
        },
      },
      path: pdfPath,
      contentType: "application/pdf",
      name: fileName,
      sha1: getSha1(pdfPath),
    },
  });

  return cancelInvoice;
};

const disconnectAllCdrsFromInvoice = async (
  invoice: InvoiceWithIncludes,
  cancelInvoice: Invoice,
) => {
  await prisma.invoice.update({
    where: {
      id: invoice.id,
    },
    data: {
      stateOfInvoice: StateOfInvoice.CANCEL,
      invoiceChilds: {
        connect: {
          id: cancelInvoice.id,
        },
      },
      cdrs: {
        disconnect: invoice.cdrs.map((cdr) => ({
          CDR_ID: cdr.CDR_ID,
        })),
      },
      creditCdrs: {
        disconnect: invoice.creditCdrs.map((cdr) => ({
          CDR_ID: cdr.CDR_ID,
        })),
      },
    },
  });
};

export async function GET(request: NextRequest, { params }: Props) {
  const invoiceId = params.slug;

  if (!env.INVOICE_FOLDER) {
    return new Response("no invoice folder defined", { status: 500 });
  }

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      contact: {
        include: {
          contactAddress: true,
        },
      },
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      cdrs: {
        include: {
          cost: true,
        },
      },
      creditCdrs: true,
    },
  });

  if (!invoice) {
    return NextResponse.json("Invoice not found", { status: 500 });
  }
  if (invoice.stateOfInvoice == StateOfInvoice.CANCEL) {
    return NextResponse.json("Invoice already canceled", { status: 500 });
  }

  const canceledInvoice = await createCancelInvoice(invoice);

  await disconnectAllCdrsFromInvoice(invoice, canceledInvoice);

  return NextResponse.json("ok");
}
