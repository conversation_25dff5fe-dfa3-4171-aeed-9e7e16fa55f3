import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Lo<PERSON> from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const job = CronJob(
  "api/cronjobs/aggregateMonthlyForecast",
  "0 0 * * *", // Daily at 00:00 (see https://crontab.guru/)
  async () => {
    Logger("Starting monthly forecast aggregation cronjob", "Monthly Forecast Aggregation", "cron", LogType.INFO);
    
    try {
      // Call the aggregation API
      const response = await fetch(`${process.env.NEXTAUTH_URL}/api/forecast/aggregate-monthly`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.INTERNAL_API_KEY || 'internal-cronjob-key',
        },
      });

      if (response.ok) {
        const result = await response.json();
        Logger(
          `Monthly forecast aggregation cronjob completed successfully: ${JSON.stringify(result.data)}`,
          "Monthly Forecast Aggregation",
          "cron",
          LogType.INFO
        );
      } else {
        const errorText = await response.text();
        <PERSON>gger(
          `Monthly forecast aggregation cronjob failed with status ${response.status}: ${errorText}`,
          "Monthly Forecast Aggregation",
          "cron",
          LogType.ERROR
        );
      }
    } catch (error) {
      Logger(
        `Monthly forecast aggregation cronjob error: ${error}`,
        "Monthly Forecast Aggregation",
        "cron",
        LogType.ERROR
      );
    }
  },
);

export const POST = job;
