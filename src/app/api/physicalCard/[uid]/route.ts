import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Role } from "@prisma/client";

interface Props {
  params: {
    uid: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const physicalCard = await prisma.physicalCard.findUnique({
      where: { uid: params.uid },
      include: {
        cpo: {
          select: {
            id: true,
            name: true,
          },
        },
        userGroup: {
          select: {
            id: true,
            name: true,
            ou: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        EMPCard: {
          select: {
            id: true,
            active: true,
            user: {
              select: {
                email: true,
              },
            },
          },
        },
      },
    });

    if (!physicalCard) {
      return NextResponse.json({ error: "Physical card not found" }, { status: 404 });
    }

    return NextResponse.json(physicalCard);
  } catch (error) {
    console.error("Error fetching physical card:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { visualNumber, valid, cpoId, userGroupId } = body;

    // Check if physical card exists
    const existingCard = await prisma.physicalCard.findUnique({
      where: { uid: params.uid },
    });

    if (!existingCard) {
      return NextResponse.json({ error: "Physical card not found" }, { status: 404 });
    }

    // Validate userGroup if provided
    if (userGroupId) {
      const userGroup = await prisma.userGroup.findUnique({
        where: { id: userGroupId },
        include: { ou: true },
      });
      if (!userGroup) {
        return NextResponse.json({ error: "UserGroup not found" }, { status: 400 });
      }

      // Check if user has access to this UserGroup's OU
      if (session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
        return NextResponse.json({ error: "UserGroup belongs to different OU" }, { status: 403 });
      }
    }

    // Check for visualNumber conflicts if it's being changed
    if (visualNumber && visualNumber !== existingCard.visualNumber) {
      const visualNumberConflict = await prisma.physicalCard.findFirst({
        where: {
          visualNumber,
          uid: { not: params.uid },
        },
      });

      if (visualNumberConflict) {
        return NextResponse.json(
          { error: "Eine Karte mit dieser Kartennummer existiert bereits" },
          { status: 409 }
        );
      }
    }

    const updatedCard = await prisma.physicalCard.update({
      where: { uid: params.uid },
      data: {
        visualNumber: visualNumber || existingCard.visualNumber,
        valid: valid !== undefined ? valid : existingCard.valid,
        cpoId: cpoId || existingCard.cpoId,
        userGroupId: userGroupId === null ? null : (userGroupId || existingCard.userGroupId),
      },
      include: {
        cpo: {
          select: {
            id: true,
            name: true,
          },
        },
        userGroup: {
          select: {
            id: true,
            name: true,
            ou: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        EMPCard: {
          select: {
            id: true,
            active: true,
            user: {
              select: {
                email: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(updatedCard);
  } catch (error) {
    console.error("Error updating physical card:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Check if physical card exists
    const existingCard = await prisma.physicalCard.findUnique({
      where: { uid: params.uid },
      include: {
        EMPCard: {
          select: {
            id: true,
            active: true,
            user: {
              select: {
                email: true,
              },
            },
          },
        },
      },
    });

    if (!existingCard) {
      return NextResponse.json({ error: "Physical card not found" }, { status: 404 });
    }

    // Delete the physical card
    // The EMPCard.physicalCardId will be automatically set to NULL due to the foreign key constraint
    await prisma.physicalCard.delete({
      where: { uid: params.uid },
    });

    return NextResponse.json({
      message: "Physical card deleted successfully",
      deletedCard: {
        uid: existingCard.uid,
        visualNumber: existingCard.visualNumber,
      }
    });
  } catch (error) {
    console.error("Error deleting physical card:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
