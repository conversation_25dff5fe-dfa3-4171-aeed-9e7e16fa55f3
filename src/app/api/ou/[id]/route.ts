import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { z } from "zod";
import prisma from "~/server/db/prisma";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";

const UpdateOuSchema = z.object({
  operatorId: z.string().max(5, "OperatorId darf maximal 5 Zeichen haben").optional().nullable(),
});

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const ou = await prisma.ou.findUnique({
      where: { 
        id: params.id,
        deleted: null,
      },
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
        externalReference: true,
        customerReference: true,
        address: true,
        city: true,
        postalCode: true,
        country: true,
        companyEmail: true,
        primaryContactperson: true,
        primaryContactpersonEmail: true,
        hotlinePhoneNumber: true,
      },
    });

    if (!ou) {
      return NextResponse.json({ error: "OU not found" }, { status: 404 });
    }

    return NextResponse.json(ou);
  } catch (error) {
    console.error("Error fetching OU:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = UpdateOuSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    const { operatorId } = result.data;

    // Check if OU exists
    const existingOu = await prisma.ou.findUnique({
      where: { 
        id: params.id,
        deleted: null,
      },
    });

    if (!existingOu) {
      return NextResponse.json({ error: "OU not found" }, { status: 404 });
    }

    // Update OU
    const updatedOu = await prisma.ou.update({
      where: { id: params.id },
      data: {
        operatorId: operatorId || null,
      },
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
      },
    });

    return NextResponse.json(updatedOu);
  } catch (error) {
    console.error("Error updating OU:", error);
    
    if (error instanceof PrismaClientKnownRequestError) {
      if (error.code === "P2016") {
        return NextResponse.json({ error: "OU not found" }, { status: 404 });
      }
    }
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
