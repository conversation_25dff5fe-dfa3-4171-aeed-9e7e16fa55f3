import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Role } from "@prisma/client";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";
import { env } from "~/env";
import { getSha1 } from "~/utils/files/getSha1";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only ADMIN users can upload energy reseller documents
  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const contactId = formData.get("contactId") as string;
    const validFrom = formData.get("validFrom") as string;
    const validTo = formData.get("validTo") as string;

    if (!file || !contactId || !validFrom || !validTo) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Validate file type (only PDF allowed)
    if (file.type !== "application/pdf") {
      return NextResponse.json({ error: "Only PDF files are allowed" }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "File size must be less than 10MB" }, { status: 400 });
    }

    // Verify contact exists
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    // Create directory structure
    const currentYear = new Date().getFullYear();
    const contactName = contact.name?.replaceAll(" ", "_").toLowerCase() ?? "unknown_contact";
    const uploadDir = path.join(env.INVOICE_FOLDER, currentYear.toString(), contactName, "energy_reseller");
    
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileName = `wiederverkaeufernachweis_${timestamp}.pdf`;
    const filePath = path.join(uploadDir, fileName);

    // Save file to disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Create FileRef entry
    const fileRef = await prisma.fileRef.create({
      data: {
        name: fileName,
        path: filePath,
        sha1: getSha1(filePath),
        contentType: "application/pdf",
      },
    });

    // Create ContactEnergyReseller entry
    const energyReseller = await prisma.contactEnergyReseller.create({
      data: {
        contactId: contactId,
        fileRefId: fileRef.id,
        start: new Date(validFrom),
        end: new Date(validTo),
        valid: true,
      },
      include: {
        fileRef: true,
      },
    });

    return NextResponse.json({
      success: true,
      energyReseller,
    });

  } catch (error) {
    console.error("Error uploading energy reseller document:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const contactId = searchParams.get("contactId");

  if (!contactId) {
    return NextResponse.json({ error: "Contact ID required" }, { status: 400 });
  }

  try {
    const energyResellers = await prisma.contactEnergyReseller.findMany({
      where: { contactId },
      include: {
        fileRef: true,
      },
      orderBy: {
        start: "desc",
      },
    });

    return NextResponse.json(energyResellers);
  } catch (error) {
    console.error("Error fetching energy reseller documents:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only ADMIN users can delete energy reseller documents
  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
  }

  try {
    const { energyResellerId } = await request.json();

    if (!energyResellerId) {
      return NextResponse.json({ error: "Energy reseller ID required" }, { status: 400 });
    }

    // Delete the energy reseller entry (FileRef will be handled separately if needed)
    await prisma.contactEnergyReseller.delete({
      where: { id: energyResellerId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting energy reseller document:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
