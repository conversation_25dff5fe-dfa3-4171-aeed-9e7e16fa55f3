import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { validateApi<PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import prisma from "~/server/db/prisma";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env.js";

// Zod Schema für Token-Objekte
const TokenObjectSchema = z.object({
  name: z.string().min(1, "Name ist erforderlich"),
  uid: z.string().min(1, "UID ist erforderlich"),
  note: z.string().optional().default(""),
});

// Schema für das Array von Token-Objekten
const TokenArraySchema = z.object({
  tokens: z.array(TokenObjectSchema).min(1, "Mindestens ein Token ist erforderlich"),
});

// Interface für Longship LocalTokenGroup
interface LocalTokenGroup {
  id: string;
  oucode: string;
  tokenGroupName: string;
  targetOUCodes: string[];
  tokens: {
    isValid: boolean;
    name: string;
    uid: string;
    contractId: string;
    normalizedContractId: string;
  }[];
  targetChargepointIds: string[];
  overrideTariffId: string;
  created: string;
  updated: string;
  isPrefix: boolean;
}

/**
 * API-Endpunkt für Token-Management über API-Key
 * Akzeptiert ein Array von Token-Objekten mit name, uid und note
 * URL: POST /api/v1/contact/tokens
 */
export async function POST(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    // Request Body parsen
    const body = await request.json();

    // Zod Validierung
    const validationResult = TokenArraySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validationResult.error.errors,
          message: "Die übermittelten Token-Daten sind ungültig",
        },
        { status: 400 },
      );
    }

    const { tokens } = validationResult.data;

    // Contact und OU Informationen
    const contact = validation.contact;
    const ouName = contact?.ou?.name || "Unknown OU";

    // TokenGroup für den Contact finden oder erstellen
    let tokenGroup = await prisma.tokenGroup.findFirst({
      where: {
        contactId: contact?.id,
      },
    });

    // Wenn keine TokenGroup existiert, erstelle eine neue
    if (!tokenGroup) {
      tokenGroup = await prisma.tokenGroup.create({
        data: {
          name: ouName,
          contactId: contact?.id,
        },
      });
    }

    // Aktuelle Zeit für die Description
    const currentDate = new Date().toLocaleString("de-DE", {
      timeZone: "Europe/Berlin",
    });

    // Tokens erstellen
    const createdTokens = [];
    const errors = [];

    for (const tokenData of tokens) {
      try {
        // Prüfen ob Token mit dieser authenticationId bereits existiert
        const existingToken = await prisma.token.findFirst({
          where: {
            authenticationId: tokenData.uid,
          },
        });

        if (existingToken) {
          // Token aktualisieren
          const updatedToken = await prisma.token.update({
            where: { id: existingToken.id },
            data: {
              name: tokenData.name,
              description: `API Token - Letztes Update: ${currentDate}${
                tokenData.note ? ` - ${tokenData.note}` : ""
              }`,
            },
          });
          createdTokens.push({ ...updatedToken, action: "updated" });
        } else {
          // Neuen Token erstellen
          const newToken = await prisma.token.create({
            data: {
              authenticationId: tokenData.uid,
              plateNumber: "", // Leer wie gewünscht
              name: tokenData.name,
              description: `API Token - Erstellt: ${currentDate}${
                tokenData.note ? ` - ${tokenData.note}` : ""
              }`,
              tokenGroupId: tokenGroup.id,
            },
          });
          createdTokens.push({ ...newToken, action: "created" });
        }
      } catch (error) {
        console.error(`Error processing token ${tokenData.uid}:`, error);
        errors.push({
          uid: tokenData.uid,
          name: tokenData.name,
          error: "Failed to create/update token",
        });
      }
    }

    // Tokens zu Longship pushen
    const longshipErrors = [];
    const longshipSuccess = [];

    try {
      // Longship LocalTokenGroups abrufen
      const headers = LongshipHeaders({});
      const fetchRequest = await fetch(`${env.LONGSHIP_API}localtokengroups?skip=0&take=100`, {
        method: "GET",
        headers: headers,
      });

      if (fetchRequest.ok) {
        const longshipTokenGroups = (await fetchRequest.json()) as LocalTokenGroup[];
        const ouCode = contact?.ou?.code;

        if (!ouCode) {
          longshipErrors.push("OU Code nicht gefunden für Longship Push");
        } else {
          // Passende LocalTokenGroup finden
          const longshipTokenGroup = longshipTokenGroups.find((group) => group.oucode === ouCode);

          if (!longshipTokenGroup) {
            longshipErrors.push(`Keine Longship TokenGroup für OU Code ${ouCode} gefunden`);
          } else {
            // Tokens zu Longship pushen
            for (const tokenData of tokens) {
              try {
                const contractId = `${contact?.companyName}`;

                const tokenPushRequest = await fetch(
                  `${env.LONGSHIP_API}localtokengroups/${longshipTokenGroup.id}/token/${tokenData.uid}`,
                  {
                    method: "PUT",
                    headers: headers,
                    body: JSON.stringify({
                      isValid: true,
                      name: tokenData.name,
                      contractId: contractId,
                    }),
                  },
                );

                if (tokenPushRequest.ok) {
                  longshipSuccess.push({
                    uid: tokenData.uid,
                    name: tokenData.name,
                    longshipTokenGroupId: longshipTokenGroup.id,
                  });
                } else {
                  const errorText = await tokenPushRequest.text();
                  longshipErrors.push({
                    uid: tokenData.uid,
                    name: tokenData.name,
                    error: `Longship Push failed: ${errorText}`,
                  });
                }
              } catch (error) {
                console.error(`Error pushing token ${tokenData.uid} to Longship:`, error);
                longshipErrors.push({
                  uid: tokenData.uid,
                  name: tokenData.name,
                  error: "Longship Push failed: Network error",
                });
              }
            }
          }
        }
      } else {
        const errorText = await fetchRequest.text();
        longshipErrors.push(`Failed to fetch Longship TokenGroups: ${errorText}`);
      }
    } catch (error) {
      console.error("Error communicating with Longship:", error);
      longshipErrors.push("Failed to communicate with Longship API");
    }

    return NextResponse.json({
      success: true,
      message: `${createdTokens.length} Token(s) erfolgreich verarbeitet`,
      contact: {
        id: contact?.id,
        name: contact?.name,
        companyName: contact?.companyName,
        ou: contact?.ou
          ? {
              id: contact.ou.id,
              name: contact.ou.name,
              code: contact.ou.code,
            }
          : null,
      },
      tokenGroup: {
        id: tokenGroup.id,
        name: tokenGroup.name,
      },
      processedTokens: createdTokens,
      errors: errors,
      longship: {
        success: longshipSuccess,
        errors: longshipErrors,
        statistics: {
          pushed: longshipSuccess.length,
          failed: longshipErrors.length,
        },
      },
      statistics: {
        total: tokens.length,
        processed: createdTokens.length,
        errors: errors.length,
        longshipPushed: longshipSuccess.length,
        longshipErrors: longshipErrors.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error processing token request:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Ein unerwarteter Fehler ist aufgetreten",
      },
      { status: 500 },
    );
  }
}
