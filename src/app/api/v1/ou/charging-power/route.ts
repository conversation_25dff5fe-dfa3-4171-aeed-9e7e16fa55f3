import { NextRequest, NextResponse } from "next/server";
import { validateApiKey } from "~/utils/apiAuth/apiKeyUtil";
import { getOusBelowOu } from "~/server/model/ou/func";
import {
  loadSessionData,
  loadChargepointstatus,
  loadLocation,
  getCurrentKw,
} from "~/pages/api/realtime2";

/**
 * API-Endpunkt für aktuelle Ladeleistung aller Ladepunkte einer OU
 * Berechnet die Ladeleistung basierend auf aktiven Sessions (wie auf der Realtime-Seite)
 * URL: GET /api/v1/ou/charging-power
 */
export async function GET(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    const contact = validation.contact;
    const contactOu = contact?.ou;

    if (!contactOu) {
      return NextResponse.json(
        {
          error: "No OU found for contact",
          message: "Contact ist keiner OU zugeordnet",
        },
        { status: 400 },
      );
    }

    // Alle OUs unterhalb der Contact-OU laden (inklusive der OU selbst)
    const ous = await getOusBelowOu(contactOu);
    const ouIds = ous.map((ou) => ou.id);

    // Session-Daten und Ladepunkt-Status laden
    const activeChargingSessions = await loadSessionData(ouIds);
    const chargepointStatusList = await loadChargepointstatus(ouIds);
    const locations = await loadLocation(ouIds);

    // Gesamte aktuelle Ladeleistung berechnen
    const totalCurrentKw = getCurrentKw(activeChargingSessions);

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      contact: {
        name: contact?.name,
      },
      chargingPower: {
        totalCurrentKw: (totalCurrentKw / 1000).toFixed(2), // Watt zu kW, 2 Dezimalstellen
        totalActiveSessions: activeChargingSessions.length,
      },
      statistics: {
        totalOusChecked: ouIds.length,

        totalActiveSessions: activeChargingSessions.length,
      },
    });
  } catch (error) {
    console.error("Error fetching charging power:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Fehler beim Abrufen der Ladeleistung",
      },
      { status: 500 },
    );
  }
}
