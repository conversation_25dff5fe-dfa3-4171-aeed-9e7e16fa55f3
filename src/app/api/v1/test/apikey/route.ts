import { NextRequest, NextResponse } from "next/server";
import { validateApi<PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";

/**
 * Test endpoint to demonstrate API key validation
 * This endpoint requires a valid API key in the Authorization header
 * URL: GET /api/v1/test/apikey
 */
export async function GET(request: NextRequest) {
  // Validate API key using utility function
  const validation = await validateApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      { 
        error: "API key validation failed", 
        message: validation.error 
      },
      { status: 401 }
    );
  }

  // API key is valid, return success response with contact info
  return NextResponse.json({
    success: true,
    message: "API key validation successful",
    timestamp: new Date().toISOString(),
    contact: {
      id: validation.contact?.id,
      name: validation.contact?.name,
      companyName: validation.contact?.companyName,
      cpo: validation.contact?.cpo,
      ou: validation.contact?.ou ? {
        id: validation.contact.ou.id,
        name: validation.contact.ou.name,
        code: validation.contact.ou.code,
      } : null,
    },
  });
}

/**
 * Test endpoint for POST requests with API key validation
 * URL: POST /api/v1/test/apikey
 */
export async function POST(request: NextRequest) {
  // Validate API key using utility function
  const validation = await validateApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      { 
        error: "API key validation failed", 
        message: validation.error 
      },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: "API key validation successful for POST request",
      timestamp: new Date().toISOString(),
      receivedData: body,
      contact: {
        id: validation.contact?.id,
        name: validation.contact?.name,
        companyName: validation.contact?.companyName,
        cpo: validation.contact?.cpo,
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: true,
      message: "API key validation successful for POST request (no JSON body)",
      timestamp: new Date().toISOString(),
      contact: {
        id: validation.contact?.id,
        name: validation.contact?.name,
        companyName: validation.contact?.companyName,
        cpo: validation.contact?.cpo,
      },
    });
  }
}
