import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session?.user?.role !== Role.ADMIN) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    // Get all OUs with basic info
    const ous = await prisma.ou.findMany({
      select: {
        id: true,
        code: true,
        name: true,
      },
      orderBy: {
        code: 'asc',
      },
    });

    // Get aggregation status for each OU
    const ouStatusPromises = ous.map(async (ou) => {
      // Get current month
      const currentDate = new Date();
      const currentMonthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      
      // Get last 24 months
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 24);
      
      const expectedMonths: string[] = [];
      const tempDate = new Date(startDate);
      
      while (tempDate <= currentDate) {
        const monthKey = `${tempDate.getFullYear()}-${String(tempDate.getMonth() + 1).padStart(2, '0')}`;
        expectedMonths.push(monthKey);
        tempDate.setMonth(tempDate.getMonth() + 1);
      }

      // Count existing aggregations
      const existingCount = await prisma.monthlyForecastAggregation.count({
        where: {
          ouCode: ou.code,
          month: {
            in: expectedMonths,
          },
        },
      });

      // Get latest aggregation
      const latestAggregation = await prisma.monthlyForecastAggregation.findFirst({
        where: {
          ouCode: ou.code,
        },
        orderBy: {
          month: 'desc',
        },
        select: {
          month: true,
          updatedAt: true,
          kWh: true,
          revenue: true,
          sessions: true,
        },
      });

      return {
        ...ou,
        aggregationStatus: {
          totalExpected: expectedMonths.length,
          existing: existingCount,
          missing: expectedMonths.length - existingCount,
          completionPercentage: Math.round((existingCount / expectedMonths.length) * 100),
          hasCurrentMonth: latestAggregation?.month === currentMonthKey,
          latestMonth: latestAggregation?.month,
          lastUpdated: latestAggregation?.updatedAt,
          hasData: latestAggregation ? (latestAggregation.kWh > 0 || latestAggregation.revenue > 0 || latestAggregation.sessions > 0) : false,
        },
      };
    });

    const ouStatuses = await Promise.all(ouStatusPromises);

    return NextResponse.json({
      status: "success",
      data: {
        ous: ouStatuses,
        summary: {
          totalOus: ous.length,
          fullyAggregated: ouStatuses.filter(ou => ou.aggregationStatus.missing === 0).length,
          partiallyAggregated: ouStatuses.filter(ou => ou.aggregationStatus.existing > 0 && ou.aggregationStatus.missing > 0).length,
          notAggregated: ouStatuses.filter(ou => ou.aggregationStatus.existing === 0).length,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching OU list:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
