import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session?.user?.role !== Role.ADMIN) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const ouCode = searchParams.get('ouCode');

    if (!ouCode) {
      return NextResponse.json({ error: "ouCode parameter is required" }, { status: 400 });
    }

    // Generate expected months for the last 24 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 24);
    
    const expectedMonths: string[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      expectedMonths.push(monthKey);
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Get existing aggregations for this OU
    const existingAggregations = await prisma.monthlyForecastAggregation.findMany({
      where: {
        ouCode: ouCode,
        month: {
          in: expectedMonths,
        },
      },
      select: {
        month: true,
        createdAt: true,
        updatedAt: true,
        kWh: true,
        revenue: true,
        sessions: true,
      },
      orderBy: {
        month: 'asc',
      },
    });

    // Find missing months
    const existingMonths = existingAggregations.map(agg => agg.month);
    const missingMonths = expectedMonths.filter(month => !existingMonths.includes(month));

    // Get current month for comparison
    const currentMonthKey = `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

    return NextResponse.json({
      status: "success",
      data: {
        ouCode,
        totalExpectedMonths: expectedMonths.length,
        existingMonths: existingMonths.length,
        missingMonths: missingMonths.length,
        currentMonth: currentMonthKey,
        missingMonthsList: missingMonths,
        existingAggregations: existingAggregations.map(agg => ({
          month: agg.month,
          isCurrentMonth: agg.month === currentMonthKey,
          hasData: agg.kWh > 0 || agg.revenue > 0 || agg.sessions > 0,
          lastUpdated: agg.updatedAt,
          created: agg.createdAt,
        })),
      },
    });
  } catch (error) {
    console.error("Error checking missing months:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
