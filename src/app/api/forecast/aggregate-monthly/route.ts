import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const thgPrice = 0.05;

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check for internal API key for cronjob access
    const apiKey = request.headers.get('x-api-key');
    const isInternalCall = apiKey === (process.env.INTERNAL_API_KEY || 'internal-cronjob-key');

    if (!isInternalCall && (!session || session?.user?.role !== Role.ADMIN)) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    Logger("Starting monthly forecast aggregation", "Monthly Forecast Aggregation", "api", LogType.INFO);

    // Get all OUs
    const ous = await prisma.ou.findMany({
      select: {
        id: true,
        code: true,
      },
    });

    // Get the date range for the last 24 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 24);
    startDate.setDate(1); // Start from the first day of the month
    startDate.setHours(0, 0, 0, 0);

    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalSkipped = 0;

    // Get the current month key for comparison
    const currentMonthKey = `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

    for (const ou of ous) {
      // Generate months for this OU
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

        // Check if aggregation already exists for this month and OU
        const existingAggregation = await prisma.monthlyForecastAggregation.findUnique({
          where: {
            month_ouCode: {
              month: monthKey,
              ouCode: ou.code,
            },
          },
        });

        // Skip if data exists and it's not the current month
        if (existingAggregation && monthKey !== currentMonthKey) {
          Logger(
            `Skipping aggregation for ${monthKey} - ${ou.code}: Data already exists`,
            "Monthly Forecast Aggregation",
            "api",
            LogType.DEBUG
          );
          totalSkipped++;
          currentDate.setMonth(currentDate.getMonth() + 1);
          continue;
        }

        // Calculate month boundaries
        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59, 999);
        
        // Get CDRs for this month and OU
        const cdrs = await prisma.cdr.findMany({
          where: {
            End_datetime: {
              gte: monthStart,
              lte: monthEnd,
            },
            OU_Code: `'${ou.code}'`, // Note: OU_Code is stored with quotes
          },
          include: {
            cost: true,
            tarif: true,
          },
        });

        // Calculate aggregated values
        const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);
        const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
        const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);
        const energyGrossMargin = revenue - energyCost;
        const thgRevenue = thgPrice * kWh;
        const totalGrossMargin = energyGrossMargin + thgRevenue;
        const sessions = cdrs.length;

        if (existingAggregation) {
          // Update existing record (current month only)
          await prisma.monthlyForecastAggregation.update({
            where: {
              id: existingAggregation.id,
            },
            data: {
              revenue,
              energyCost,
              energyGrossMargin,
              thgRevenue,
              totalGrossMargin,
              kWh,
              sessions,
              updatedAt: new Date(),
            },
          });
          Logger(
            `Updated aggregation for ${monthKey} - ${ou.code}`,
            "Monthly Forecast Aggregation",
            "api",
            LogType.DEBUG
          );
        } else {
          // Create new record
          await prisma.monthlyForecastAggregation.create({
            data: {
              month: monthKey,
              ouCode: ou.code,
              revenue,
              energyCost,
              energyGrossMargin,
              thgRevenue,
              totalGrossMargin,
              kWh,
              sessions,
            },
          });
          Logger(
            `Created aggregation for ${monthKey} - ${ou.code}`,
            "Monthly Forecast Aggregation",
            "api",
            LogType.DEBUG
          );
        }

        totalProcessed++;
        if (kWh > 0 || revenue > 0) {
          totalUpdated++;
        }

        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    Logger(
      `Monthly forecast aggregation completed. Processed: ${totalProcessed}, Skipped: ${totalSkipped}, Updated: ${totalUpdated}`,
      "Monthly Forecast Aggregation",
      "api",
      LogType.INFO
    );

    return NextResponse.json({
      status: "success",
      message: `Monthly forecast aggregation completed successfully`,
      data: {
        totalProcessed,
        totalSkipped,
        totalUpdated,
        ousProcessed: ous.length,
      },
    });
  } catch (error) {
    Logger(
      `Error in monthly forecast aggregation: ${error}`,
      "Monthly Forecast Aggregation",
      "api",
      LogType.ERROR
    );
    console.error("Error in monthly forecast aggregation:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error during aggregation",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
