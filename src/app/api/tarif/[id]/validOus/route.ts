import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { z } from "zod";
import prisma from "~/server/db/prisma";

const AssignValidOusSchema = z.object({
  ouIds: z.array(z.string()),
});

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get tarif with current validOus
    const tarif = await prisma.tarif.findUnique({
      where: { id: params.id },
      include: {
        validOus: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!tarif) {
      return NextResponse.json({ error: "Tarif not found" }, { status: 404 });
    }

    // Get all available OUs
    const allOus = await prisma.ou.findMany({
      where: {
        deleted: null,
      },
      select: {
        id: true,
        name: true,
        code: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({
      assigned: tarif.validOus,
      available: allOus,
    });
  } catch (error) {
    console.error("Error fetching tarif validOus:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = AssignValidOusSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    const { ouIds } = result.data;

    // Check if tarif exists
    const existingTarif = await prisma.tarif.findUnique({
      where: { id: params.id },
    });

    if (!existingTarif) {
      return NextResponse.json({ error: "Tarif not found" }, { status: 404 });
    }

    // Validate that all OUs exist
    if (ouIds.length > 0) {
      const existingOus = await prisma.ou.findMany({
        where: {
          id: { in: ouIds },
          deleted: null,
        },
      });

      if (existingOus.length !== ouIds.length) {
        return NextResponse.json({ error: "One or more OUs not found" }, { status: 400 });
      }
    }

    // Update tarif validOus
    await prisma.tarif.update({
      where: { id: params.id },
      data: {
        validOus: {
          set: ouIds.map((ouId) => ({ id: ouId })),
        },
      },
    });

    // Return updated assignments
    const updatedTarif = await prisma.tarif.findUnique({
      where: { id: params.id },
      include: {
        validOus: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "OU-Zuordnungen erfolgreich aktualisiert",
      validOus: updatedTarif?.validOus || [],
    });
  } catch (error) {
    console.error("Error updating tarif validOus:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
