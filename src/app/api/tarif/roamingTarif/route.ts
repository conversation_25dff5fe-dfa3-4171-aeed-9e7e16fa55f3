import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { KindOfTarif } from "@prisma/client";

export async function POST(request: NextRequest) {
  const { data } = await request.json();
  if (data) {
    try {
      const { validOuIds, ...tarifData } = data;

      if (data?.id) {
        // Update existing tarif
        await prisma.tarif.update({
          where: {
            id: data?.id || 0,
          },
          data: {
            ...tarifData,
            kwh: +tarifData.kwh,
            minChargingTime: +tarifData.minChargingTime,
            minChargingEnergy: +tarifData.minChargingEnergy,
            sessionFee: +tarifData.sessionFee,
            validFrom: new Date(tarifData.validFrom),
            validTo: new Date(tarifData.validTo),
            blockingFee: +tarifData.blockingFee,
            blockingFeeMax: +tarifData.blockingFeeMax,
            blockingFeeBeginAtMin: +tarifData.blockingFeeBeginAtMin,
            kindOfTarif: tarifData.kindOfTarif,
            contractId: tarifData.kindOfTarif == KindOfTarif.DIRECT ? tarifData.contractId : null,
            validOus: validOuIds && validOuIds.length > 0 ? {
              set: validOuIds.map((ouId: string) => ({ id: ouId }))
            } : {
              set: []
            }
          },
        });
      } else {
        // Create new tarif
        await prisma.tarif.create({
          data: {
            ...tarifData,
            kwh: +tarifData.kwh,
            sessionFee: +tarifData.sessionFee,
            minChargingTime: +tarifData.minChargingTime,
            minChargingEnergy: +tarifData.minChargingEnergy,
            validFrom: new Date(tarifData.validFrom),
            validTo: new Date(tarifData.validTo),
            blockingFee: +tarifData.blockingFee,
            blockingFeeMax: +tarifData.blockingFeeMax,
            blockingFeeBeginAtMin: +tarifData.blockingFeeBeginAtMin,
            kindOfTarif: tarifData.kindOfTarif,
            contractId: tarifData.kindOfTarif == KindOfTarif.DIRECT ? tarifData.contractId : null,
            validOus: validOuIds && validOuIds.length > 0 ? {
              connect: validOuIds.map((ouId: string) => ({ id: ouId }))
            } : undefined
          },
        });
      }
      return NextResponse.json(data);
    } catch (e: unknown) {
      if (e instanceof Error) {
        return NextResponse.json(
          { success: false, message: `DB Error: ${e.message} ` },
          { status: 500 },
        );
      }
      return NextResponse.json({ success: false, message: `DB Error` }, { status: 500 });
    }
  } else {
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
