import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { authOptions } from "~/server/auth";

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const { selectedTarifIds } = await request.json();
  
  if (!selectedTarifIds || !Array.isArray(selectedTarifIds)) {
    return new Response("Invalid tarif selection", { status: 400 });
  }

  try {
    // Finde die inaktive EMPCard des Benutzers
    const empCard = await prisma.eMPCard.findFirst({
      where: {
        userId: session.user.id,
        active: false,
        preDelivered: true,
        activatedAt: null
      }
    });

    if (!empCard) {
      return new Response("No inactive card found", { status: 404 });
    }

    // Verknüpfe die ausgewählten Tarife mit der EMPCard
    await prisma.eMPCard.update({
      where: { id: empCard.id },
      data: {
        tarifs: {
          create: selectedTarifIds.map((tid: string) => {
            return { tarif: { connect: { id: tid } } };
          }),
        }
      }
    });

    return new Response("Tarifs assigned successfully", { status: 200 });
  } catch (error) {
    console.error("Error assigning tarifs:", error);
    return new Response("Error assigning tarifs", { status: 500 });
  }
}