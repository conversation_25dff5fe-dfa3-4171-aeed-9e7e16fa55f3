import type { NextRequest } from "next/server";
import prisma from "../../../../../server/db/prisma";
import { NextResponse } from "next/server";
import { endOfDay, getFirstDayOfLastMonth, startOfDay } from "../../../../../utils/date/date";

interface Props {
  params: {
    slug: string;
  };
}

//TODO error handling

export async function PUT(request: NextRequest, { params }: Props) {
  const locationId = params.slug;

  const { contract } = await request.json();
  try {
    await prisma.powerContract.create({
      data: {
        ...contract,
        monthlyFixCost: parseFloat(contract.monthlyFixCost),
        baseKWhPrice: parseFloat(contract.baseKWhPrice),
        kwhPrice: parseFloat(contract.kwhPrice),
        start: startOfDay(contract.start),
        end: endOfDay(contract.end),
        locationId: locationId,
      },
    });
  } catch (error) {
    return NextResponse.json("Prisma DB Error");
  }

  return NextResponse.json("ok");
}
