generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "Account_userId_fkey")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "Session_userId_fkey")
}

enum Role {
  USER
  ADMIN
  CARD_HOLDER
  CARD_MANAGER
  CPO
}



model User {
  id                   String               @id @default(cuid())
  name                 String
  lastName             String               @default("")
  email                String               @unique
  password             String?
  emailVerified        DateTime?
  image                String?
  role                 Role                 @default(USER)
  accounts             Account[]
  sessions             Session[]
  ou                   Ou                   @relation(fields: [ouId], references: [id])
  ouId                 String
  selectedOu           Ou                   @relation("SelectedOu", fields: [selectedOuId], references: [id])
  selectedOuId         String
  phone                String?
  paymentMethods       PaymentMethod[]
  signUpHash           String?              @unique
  stripeCustomerId     String?
  address               ContactAddress[]
  createdAt            DateTime             @default(now()) @map("created_at")
  updatedAt            DateTime             @default(now()) @updatedAt @map("updated_at")
  empCards             EMPCard[]
  invoices             Invoice[]
  AgGridView           AgGridView[]
  identifier           String?
  signUpHashValidUntil DateTime?
  remoteStartToken     String?
  companyName          String?
  systemNotifications  SystemNotification[]
  userGroupId          String?
  userGroup            UserGroup?           @relation(fields: [userGroupId], references: [id])
  maintenanceRecords   MaintenanceRecord[]

  @@index([ouId], map: "User_ouId_fkey")
}

model EMPCard {
  id             String                  @id @default(cuid())
  active         Boolean                 @default(false)
  deactivatedAt  DateTime?               @map("deactivated_at")
  orderedAt      DateTime                @default(now()) @map("ordered_at")
  userId         String?
  user           User?                   @relation(fields: [userId], references: [id])
  contactId      String?
  contact        Contact?                @relation(fields: [contactId], references: [id])
  physicalCardId String?                 @unique
  physicalCard   PhysicalCard?           @relation(fields: [physicalCardId], references: [uid])
  tarifs         CompanyTarifOnEMPCard[]
  invoiceId      String?
  invoice        Invoice?                @relation(fields: [invoiceId], references: [id])
  activatedAt    DateTime?               @map("activated_at")
  sentOut        DateTime?               @map("sentOut_at")
  note           String?
  preDelivered   Boolean                @default(false)
}

model PhysicalCard {
  uid          String     @id
  visualNumber String     @unique
  valid        Boolean    @default(true)
  cpoId        String?
  cpo          Contact?   @relation(fields: [cpoId], references: [id])
  EMPCard      EMPCard?   // Add this field to represent the relation
  userGroupId  String?
  userGroup    UserGroup? @relation(fields: [userGroupId], references: [id])
}

model PaymentMethod {
  id                    String   @id @default(cuid())
  userId                String   @map("user_id")
  user                  User     @relation(fields: [userId], references: [id])
  stripePaymentMethodId String   @map("stripe_payment_method_id")
  isActive              Boolean  @default(false)
  hidden                Boolean  @default(false)
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([userId, stripePaymentMethodId], map: "unique_user_stripe_method")
}

model Ou {
  id                        String             @id @default(cuid())
  parentId                  String?
  name                      String
  code                      String
  externalReference         String?
  customerReference         String?
  gridOwnerReference        String?
  tenantReference           String?
  address                   String?
  state                     String?
  country                   String?
  city                      String?
  houseNumber               String?
  postalCode                String?
  hotlinePhoneNumber        String?
  companyEmail              String?
  primaryContactperson      String?
  primaryContactpersonEmail String?
  directPaymentProfileId    String?
  mspOuId                   String?
  mspOuName                 String?
  mspOuCode                 String?
  mspExternalId             String?
  operatorId                String?            @db.VarChar(5)
  financialDetails          Json?
  historyPowerByOu          HistoryPowerByOu[]
  User                      User[]
  SelectedUsers             User[]             @relation("SelectedOu")
  AgGridView                AgGridView[]
  TokenGroups               TokenGroup[]       @relation("BillingOUs")
  Evse                      Evse[]
  Contact                   Contact?
  CompanyTarif              CompanyTarif[]
  Location                  Location[]
  UserGroups                UserGroup[]
  deleted                   DateTime?
  hide                      Boolean @default(false)
  ouType                    String?
  allowOrderEMPCards        Boolean @default(false)
  registrationSlug         String?  @unique
  defaultEMPCardOneTimeFee  Float @default(0)
  maintenanceRecords        MaintenanceRecord[]
  // Many-to-many relation with Tarifs
  validTarifs               Tarif[] @relation("TarifValidOus")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum InvoiceLanguageCode {
  DE
  EN
}

enum CdrMapping {
  Standard
  EnBW
  Standard_Credit
}

model OCPPErrorStatusMessage {
  messageId       String   @id
  connectorId     Int
  status          String
  errorCode       String
  info            String
  timestamp       DateTime @db.DateTime(0)
  chargePointId   String
  wampMessageType String
  ocppMessageType String
}

model Contact {
  id                     String                  @id @unique @default(cuid())
  customerNumber         String?
  supplierNumber         String?
  name                   String?                 @db.VarChar(255)
  providers              Provider[]
  companyName            String?                 @db.VarChar(200)
  invoiceMail            String                  @default("<EMAIL>") @db.VarChar(200)
  cdrMail                String?                 @db.VarChar(200)
  note                   String?                 @db.LongText
  invoiceLanguageCode    InvoiceLanguageCode     @default(DE)
  contactAddress         ContactAddress[]
  contactEnergyResellers ContactEnergyReseller[]
  invoices               Invoice[]
  tarifs                 TarifOnContacts[]
  tokenGroup             TokenGroup[]
  cdrMapping             CdrMapping              @default(Standard)
  ou                     Ou?                     @relation(fields: [ouId], references: [id])
  ouId                   String? @unique
  cpo                    Boolean                 @default(false)
  creditTarifs           ContactCreditTarif[]
  noRoaming              Boolean                 @default(false)
  empCards               EMPCard[]
  cpoContracts           CPOContract[]
  stripeCustomerId       String?
  sepaHash               String?
  physicalCards          PhysicalCard[]
  iban                   String?
  adhocEmpId             String?                 @db.VarChar(255)
  visibleInRoamingMatrix Boolean                 @default(false)
  apiKey                 String?                 @unique @db.VarChar(255)

}

model Provider {
  providerId        String   @db.VarChar(3)
  providerCountryId String   @db.VarChar(3)
  // Weitere Felder nach Bedarf
  contactId         String?
  contact           Contact? @relation(fields: [contactId], references: [id])

  @@id([providerId, providerCountryId])
}

model ContactEnergyReseller {
  id        String    @id @unique @default(cuid())
  start     DateTime? @db.DateTime(0)
  end       DateTime? @db.DateTime(0)
  fileRef   FileRef?  @relation(fields: [fileRefId], references: [id])
  fileRefId String?
  valid     Boolean?
  contactId String
  contact   Contact   @relation(fields: [contactId], references: [id])

  @@index([contactId], map: "ContactEnergyReseller_contactId_fkey")
}

enum KindOfInvoice {
  INVOICE
  STORNO
  CREDIT
  CREDIT_STORNO
}

enum StateOfInvoice {
  PREVIEW
  CREATED
  PAID
  CANCEL
}

model Invoice {
  id String @id @unique @default(cuid())

  invoiceId      Int?
  invoiceNumber  String? @unique
  invoiceIdSufix String?

  invoiceRelationId String?   @unique
  invoiceParent     Invoice?  @relation("InvoiceRelation", fields: [invoiceRelationId], references: [id])
  invoiceChilds     Invoice[] @relation("InvoiceRelation")

  kindOfInvoice     KindOfInvoice
  stateOfInvoice    StateOfInvoice
  createDate        DateTime?      @db.Date
  invoiceDate       DateTime?      @db.Date
  servicePeriodFrom DateTime?      @db.Date
  servicePeriodTo   DateTime?      @db.Date

  paidOnDate DateTime? @db.Date
  paidAmount Float?
  sendAsMail Boolean   @default(false)

  sumKwh     Float
  sumSession Int

  sumNet   Float
  sumGross Float
  sumTax   Float

  subject String?

  contactId String?
  contact   Contact? @relation(fields: [contactId], references: [id])

  invoicePositions InvoicePosition[]
  cdrs             Cdr[]
  creditCdrs       Cdr[]                  @relation("CreditInvoiceRelation")
  cpoCdrs          Cdr[]                  @relation("CpoInvoiceRelation")
  chargePoints     ChargePointToInvoice[] // Liste der ChargePoint-IDs, die in dieser Rechnung enthalten sind

  history String? @db.LongText

  files            FileRef[]
  bankTransactions InvoiceToQontoTransaction[]
  userId           String?
  user             User?                       @relation(fields: [userId], references: [id])
  paymentIntent    PaymentIntent?
  empCards         EMPCard[]
  tokens           Token[]
  contractId       String?
  contract         CPOContract?                @relation(fields: [contractId], references: [id])

  @@unique([invoiceId, invoiceIdSufix])
}

model PaymentIntent {
  id        String  @id @unique
  status    String
  invoiceId String?  @unique
  invoice   Invoice? @relation(fields: [invoiceId], references: [id])
  cdrId     String? @unique
}

model InvoicePosition {
  id          String   @id @unique @default(cuid())
  pos         Int?
  title       String
  amount      Float
  unit        String?
  unitPrice   Float
  description String?  @db.LongText
  sumNet      Float
  sumGross    Float
  sumTax      Float
  invoiceId   String?
  invoice     Invoice? @relation(fields: [invoiceId], references: [id])
  tarif       Tarif?   @relation(fields: [tarifId], references: [id])
  tarifId     String?
  taxRate     Float
}

model TarifOnContacts {
  tarifId   String
  contactId String
  contact   Contact @relation(fields: [contactId], references: [id])
  tarif     Tarif   @relation(fields: [tarifId], references: [id])

  @@id([tarifId, contactId])
  @@index([contactId], map: "TarifOnContacts_contactId_fkey")
}

model Tarif {
  id                    String            @id @unique @default(cuid())
  name                  String?           @unique
  sessionFee            Float?            @db.Float
  kwh                   Float             @db.Float
  minChargingTime       Float             @db.Float
  minChargingEnergy     Float             @db.Float
  blockingFee           Float             @default(0) @db.Float
  blockingFeeBeginAtMin Int               @default(0) @db.UnsignedSmallInt
  blockingFeeMax        Float             @default(0) @db.Float
  currentType           String?           @db.VarChar(11)
  validFrom             DateTime          @db.Date
  validTo               DateTime          @db.Date
  contacts              TarifOnContacts[]
  cdrs                  Cdr[]
  invoicePositions      InvoicePosition[]
  kindOfTarif           KindOfTarif       @default(ROAMING)
  contractId            String?
  contract              CPOContract?      @relation(fields: [contractId], references: [id])
  // Many-to-many relation with OUs
  validOus              Ou[]              @relation("TarifValidOus")
}

model CompanyTarif {
  id                    String                     @id @unique @default(cuid())
  name                  String
  energyPrice           Float                      @default(0) @db.Float
  sessionPrice          Float                      @default(0) @db.Float
  minChargingTime       Float                      @default(0) @db.Float
  minChargingEnergy     Float                      @default(0) @db.Float
  blockingFee           Float                      @default(0) @db.Float
  blockingFeeBeginAtMin Int                        @default(0) @db.UnsignedSmallInt
  blockingFeeMax        Float                      @default(0) @db.Float
  currentType           String?                    @db.VarChar(11)
  validFrom             DateTime                   @db.Date
  validTo               DateTime                   @db.Date
  empCards              CompanyTarifOnEMPCard[]
  tokens                CompanyTarifOnToken[]
  optional              Boolean                    @default(false)
  description           String?                    @db.VarChar(200)
  ouId                  String
  ou                    Ou                         @relation(fields: [ouId], references: [id])
  Cdr                   Cdr[]
  oneTimeFee            Float                      @default(0) @db.Float
  basicFee              Float                      @default(0) @db.Float
  oneTimeFeePayer       Role                       @default(CARD_HOLDER)
  kindOfTarif           KindOfTarif                @default(COMPANY)
  internal              Boolean                    @default(false)
  userGroups            CompanyTarifOnUserGroup[]
}

model CompanyTarifOnEMPCard {
  tarifId   String
  tarif     CompanyTarif @relation(fields: [tarifId], references: [id])
  empCardId String
  empCard   EMPCard      @relation(fields: [empCardId], references: [id])

  @@id([tarifId, empCardId])
}

model CompanyTarifOnToken {
  tarifId String
  tarif   CompanyTarif @relation(fields: [tarifId], references: [id])
  tokenId String
  token   Token        @relation(fields: [tokenId], references: [id])

  @@id([tarifId, tokenId])
}

model UserGroup {
  id            String                     @id @default(cuid())
  name          String
  description   String?                    @db.Text
  ouId          String
  ou            Ou                         @relation(fields: [ouId], references: [id])
  users         User[]
  physicalCards PhysicalCard[]
  companyTarifs CompanyTarifOnUserGroup[]
  createdAt     DateTime                   @default(now())
  updatedAt     DateTime                   @default(now()) @updatedAt

  @@unique([name, ouId])
}

model CompanyTarifOnUserGroup {
  tarifId     String
  tarif       CompanyTarif @relation(fields: [tarifId], references: [id])
  userGroupId String
  userGroup   UserGroup    @relation(fields: [userGroupId], references: [id])

  @@id([tarifId, userGroupId])
}

model FileRef {
  id          String  @id @unique @default(cuid())
  name        String
  path        String
  sha1        String?
  contentType String?

  invoice                Invoice?                @relation(fields: [invoiceId], references: [id])
  invoiceId              String?
  stripePayout           StripePayout?           @relation(fields: [stripePayoutId], references: [id])
  stripePayoutId         String?
  cpoContractId          String?
  cpoContract            CPOContract?            @relation(fields: [cpoContractId], references: [id])
  contactEnergyResellers ContactEnergyReseller[]
}

model FileStorage {
  id   Int  @id @default(autoincrement()) @db.UnsignedInt
  path Int?
}

model Cdr {
  CDR_ID               String        @id @default("")
  Start_datetime       DateTime      @db.DateTime(0)
  End_datetime         DateTime      @db.DateTime(0)
  Duration             String?       @db.VarChar(200)
  DurationInSec        Int?          @db.Int
  Volume               Float?        @db.Float
  Charge_Point_Address String?       @db.VarChar(200)
  Charge_Point_ZIP     String?       @db.VarChar(11)
  Charge_Point_City    String?       @db.VarChar(60)
  Charge_Point_Country String?       @db.VarChar(6)
  Charge_Point_Type    Int?          @db.TinyInt
  Product_Type         Int?          @db.TinyInt
  Tariff_Type          String?       @db.Char(11)
  Authentication_ID    String?       @db.VarChar(60)
  Contract_ID          String?       @db.VarChar(60)
  Meter_ID             String?       @db.VarChar(60)
  OBIS_Code            String?       @db.VarChar(60)
  Charge_Point_ID      String?       @db.Char(36)
  Service_Provider_ID  String?       @db.Char(60)
  Infra_Provider_ID    String?       @db.Char(10)
  Calculated_Cost      Float?        @db.Float
  Timezone             String?       @db.Char(60)
  LocalStart_datetime  DateTime?     @db.DateTime(0)
  LocalEnd_datetime    DateTime?     @db.DateTime(0)
  Location_ID          String?       @db.Char(60)
  OU_Code              String?       @db.Char(60)
  Tariff_Name          String?       @db.Char(200)
  Start_Tariff         Float?        @db.Float
  Tariff_kWh           Float?        @db.Float
  Token_OU_Code        String?       @db.VarChar(60)
  Token_OU_Name        String?       @db.VarChar(100)
  OU_Name              String?       @db.Char(100)
  Owner                String?       @db.Char(100)
  Operator             String?       @db.Char(100)
  Sub_Operator         String?       @db.Char(100)
  MeterStart           Int?
  MeterStop            Int?
  ExternalReference    String?       @db.VarChar(100)
  Charging_Time_Cost   Float?        @db.Float
  Parking_Time_Cost    Float?        @db.Float
  ConnectorId          Int?
  cost                 CdrCost?
  invoice              Invoice?      @relation(fields: [invoiceId], references: [id], onDelete: SetNull)
  invoiceId            String?
  creditInvoiceId      String?
  creditInvoice        Invoice?      @relation("CreditInvoiceRelation", fields: [creditInvoiceId], references: [id])
  creditPayout         CdrPayout?
  cpoInvoiceId         String?
  cpoInvoice           Invoice?      @relation("CpoInvoiceRelation", fields: [cpoInvoiceId], references: [id])
  tarif                Tarif?        @relation(fields: [tarifId], references: [id], onDelete: SetNull)
  tarifId              String?
  billable             Boolean       @default(true)
  EnergyCosts          Float?        @db.Float
  companyTarif         CompanyTarif? @relation(fields: [companyTarifId], references: [id], onDelete: SetNull)
  companyTarifId       String?
  transactionId         String?

}

model ContactAddress {
  id             String   @id @unique @default(cuid())
  validFrom      DateTime @db.Date
  validTo        DateTime @db.Date
  street         String?  @db.VarChar(200)
  streetNr       String?  @db.VarChar(20)
  city           String?  @db.VarChar(200)
  zip            String?  @db.VarChar(200)
  country        String?  @db.VarChar(200)
  ustId          String?  @db.VarChar(200)
  isNetInvoice   Boolean?
  invoiceTaxRate Float?   @default(19)
  ustIdValid     Boolean?
  contactId      String?
  contact        Contact? @relation(fields: [contactId], references: [id])
  userId         String?
  user           User?    @relation(fields: [userId], references: [id])
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([contactId], map: "ContactAddress_contactId_fkey")
}


model ParkingEvent {
  device_id  BigInt
  timestamp  DateTime  @db.DateTime(0)
  state      Int       @db.UnsignedTinyInt
  battery    Int?      @db.UnsignedTinyInt
  sensorTime DateTime? @db.DateTime(0)
  heartbeat  Boolean   @default(false)
  init       Boolean   @default(false)

  @@id([device_id, timestamp])
}

model ParkingSensor {
  id          String   @id @default(cuid())
  name        String   @default("auto_created") @db.VarChar(191)
  evse        Evse?    @relation(fields: [evseId], references: [evse_id])
  evseId      String?
  device_id   BigInt   @unique
  state       Int      @db.UnsignedTinyInt
  lastEventAt DateTime @default(now()) @db.DateTime(0)
}

model StopLog {
  id            String    @id @default("") @db.VarChar(36)
  createAt      DateTime? @db.DateTime(0)
  transactionId Int?
  idTag         String?   @db.VarChar(255)
  chargePointId String?   @db.VarChar(255)
  emp           String?   @db.VarChar(6)
  kwh           Float?    @db.Float
  duration      Int?
  durationHr    String?   @db.VarChar(30)
}

model ChargePointError {
  id            String      @id @default(cuid())
  error         String
  createdAt     DateTime    @default(now()) // automatisch beim Erstellen gesetzt
  chargePoint   ChargePoint @relation(fields: [chargePointId], references: [id])
  chargePointId String
}

model MonitoringEvent {
  message String
  type String
  createdAt DateTime @default(now()) // automatisch beim Erstellen gesetzt
  lastevent BigInt
  evseId String
  href String?
  resolved Boolean @default(false)
  cdrId String @default("")
  @@id([lastevent, evseId,type])
}

model ChargePointToInvoice {
  chargePointId String
  invoiceId     String
  chargePoint   ChargePoint @relation(name: "ChargePointToInvoiceChargePoint", fields: [chargePointId], references: [id])
  invoice       Invoice     @relation(fields: [invoiceId], references: [id])

  @@id([chargePointId, invoiceId])
}

model ChargePoint {
  id                      String                  @id
  chargePointId           String                  @unique
  dateDeleted             DateTime?
  displayName             String
  roamingName             String
  chargeBoxSerialNumber   String?
  chargePointModel        String
  chargePointSerialNumber String?
  chargePointVendor       String
  firmwareVersion         String
  connectivityStatus      String
  iccid                   String?
  imsi                    String?
  meterSerialNumber       String?
  meterType               String?
  tenantId                String
  isRoaming               Boolean
  hasGuestUsage           Boolean?
  locationId              String?
  allowAnyToken           Boolean
  dateCreated             DateTime
  updated                 DateTime?
  ou                      String
  ouId                    String
  ouName                  String
  tariffId                String?
  tariffName              String?
  startTariff             Int?
  tariffPrice             Float?
  simCardNumber           String?
  isNew                   Boolean
  hasReimbursement        Boolean?
  reimburseTariffId       String?
  reimburseTariffName     String?
  reimburseTariffPrice    Float?
  reimburseUID            String?
  reimburseTokenId        String?
  reimburseOU             String?
  useTenantFee            Boolean
  maxCapacityInKw         Int
  historyPowerByCharger   HistoryPowerByCharger[]
  evses                   Evse[]
  invoices                ChargePointToInvoice[]  @relation(name: "ChargePointToInvoiceChargePoint")
  ChargePointError        ChargePointError[]
  productId               String?
  isQuarantined           Boolean?
  lastKnownOcppVersion    String?
}

model RealtimeData {
  id           String   @id
  status       String
  kw           Float
  kwh          Float
  sessionStart DateTime
  lastUpdate   DateTime
  emp          String
}

model Location {
  id                  String          @id @unique @default(cuid())
  country_code        String
  party_id            String
  publish             Boolean
  publish_allowed_to  String?
  name                String
  houseNumber         String
  street              String
  city                String
  postal_code         String
  country             String
  hotline_phonenumber String
  parking_type        String
  coordinates         Coordinates?    @relation(fields: [coordinatesId], references: [id])
  coordinatesId       String?
  evses               Evse[]
  powerContract       PowerContract[]
  note                String?         @db.LongText
  openingDate         DateTime?
  ou                  Ou              @relation(fields: [ouId], references: [id])
  ouId                String          @default("22d745ce-5b67-4196-a753-6c8ad24fd71a") //Eulektro Public
  maintenanceRecords  MaintenanceRecord[]
}

model PowerContract {
  id                    String   @id @unique @default(cuid())
  typeOfContract        String // Stock or Contract
  start                 DateTime
  end                   DateTime
  monthlyFixCost        Float?
  baseKWhPrice          Float?
  kwhPrice              Float?
  contractWith          String?
  contractNumber        String?
  location              Location @relation(fields: [locationId], references: [id])
  locationId            String
  customerNumber        String
  supplyNetworkOperator String
}

model ThgPrice {
  start DateTime @id @unique
  end   DateTime @unique
  price Float
}

model CdrCost {
  cost  Float
  cdr   Cdr    @relation(fields: [cdrId], references: [CDR_ID])
  cdrId String @id @unique
}

model CdrPayout {
  energyPayout   Float
  sessionPayout  Float
  blockingPayout Float
  cdr            Cdr    @relation(fields: [cdrId], references: [CDR_ID])
  cdrId          String @id @unique
  payoutSum      Float
  stripeFee      Float?
}

model HistoryPowerByCharger {
  power         Int
  timestamp     DateTime
  chargePoint   ChargePoint @relation(fields: [chargePointId], references: [id])
  chargePointId String

  @@unique([timestamp, chargePointId])
}

model HistoryPowerByOu {
  power     Int
  timestamp DateTime
  ou        Ou       @relation(fields: [ouId], references: [id])
  ouId      String

  @@unique([timestamp, ouId])
}

model Coordinates {
  id        String     @id @unique @default(cuid())
  latitude  Float
  longitude Float
  Location  Location[]
  Evse      Evse[]
}

model Evse {
  uid                String          @id @default(cuid())
  evse_id            String          @unique
  status             String
  physical_reference String?
  last_updated       DateTime
  location           Location        @relation(fields: [locationId], references: [id])
  locationId         String
  coordinates        Coordinates?    @relation(fields: [coordinatesId], references: [id])
  coordinatesId      String?
  connector          String?
  chargePoint        ChargePoint?    @relation(fields: [chargePointId], references: [id])
  chargePointId      String?
  parkingSensor      ParkingSensor[]
  ouId               String?         @default("")
  Ou                 Ou?             @relation(fields: [ouId], references: [id])
  maintenanceRecords MaintenanceRecord[]
}

model EnergyStockPrice {
  timestamp DateTime @unique
  amount    Float
  hour      Int
  day       Int
  month     Int
  year      Int
}

enum LogType {
  WARN
  ERROR
  INFO
  DEBUG
}

enum KindOfTarif {
  ROAMING
  COMPANY
  CREDIT
  DIRECT
}

model Log {
  id        Int      @id @default(autoincrement()) @db.UnsignedInt
  timestamp DateTime
  type      LogType
  topic     String
  headline  String
  message   String   @db.LongText
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

model SystemNotification {
  id        String           @id @default(cuid())
  datum     DateTime         @default(now())
  type      NotificationType @default(INFO)
  nachricht String           @db.Text
  gelesen   Boolean          @default(false)
  userId    String?
  user      User?            @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @default(now()) @updatedAt

  @@index([userId, gelesen])
  @@index([createdAt])
}

model MaintenanceCategory {
  id                String              @id @default(cuid())
  name              String              @unique
  description       String?
  isDefault         Boolean             @default(false)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @default(now()) @updatedAt
  maintenanceRecords MaintenanceRecord[]
}

model MaintenanceRecord {
  id                  String              @id @default(cuid())
  date                DateTime
  description         String              @db.Text
  notes               String?             @db.Text
  targetType          MaintenanceTargetType
  locationId          String?
  evseId              String?
  connectorId         String?
  categoryId          String
  category            MaintenanceCategory @relation(fields: [categoryId], references: [id])
  ouId                String
  ou                  Ou                  @relation(fields: [ouId], references: [id])
  userId              String
  user                User                @relation(fields: [userId], references: [id])
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @default(now()) @updatedAt
  nextDueDate         DateTime?           // For DGUV-V3 reminders

  // Relations to actual entities
  location            Location?           @relation(fields: [locationId], references: [id])
  evse                Evse?               @relation(fields: [evseId], references: [uid])

  @@index([ouId])
  @@index([categoryId])
  @@index([targetType])
  @@index([nextDueDate])
}

enum MaintenanceTargetType {
  LOCATION
  EVSE
  CONNECTOR
  ALL_EVSES
}

model Token {
  id               String                @id @default(cuid())
  createDate       DateTime              @default(now())
  authenticationId String?
  plateNumber      String
  name             String?
  description      String?               @db.Text
  tokenGroupId     String
  tokenGroup       TokenGroup            @relation(fields: [tokenGroupId], references: [id])
  tarifs           CompanyTarifOnToken[]
  invoiceId        String?
  invoice          Invoice?              @relation(fields: [invoiceId], references: [id])
}

model TokenGroup {
  id         String   @id @default(cuid())
  name       String
  tokens     Token[]
  contactId  String?
  contact    Contact? @relation(fields: [contactId], references: [id])
  billingOUs Ou[]     @relation("BillingOUs")
}

enum PayoutStatus {
  CREATED
  STRIPE_REPORT_TRIGGERED
  STRIPE_REPORT_RECEIVED
  STRIPE_FILE_DOWNLOADED
  TAX_FILE_GENERATED
  TAX_FILE_UPLOADED
  ERROR_INVOICES_DO_NOT_MATCH
}

model StripePayout {
  id               String                           @id
  amount           Int
  arrival_date     DateTime
  created          DateTime
  automatic        Boolean
  status           PayoutStatus                     @default(CREATED)
  bankTransactions StripePayoutToQontoTransaction[]
  FileRef          FileRef[]
}

model StripePayoutItem {
  automaticPayoutId    String
  paymentIntentId      String
  gross                Float
  net                  Float
  fee                  Float
  customerFacingAmount Float
  paymentMetadataEvse  String?
  reportingCategory    String
  paymentMetadataCdrId String?
  adhocInvoiceNumber   String?
  eulektroInvoice       String?

  @@id([automaticPayoutId, paymentIntentId, reportingCategory])
}

model QontoTransaction {
  id                             String                           @unique
  transaction_id                 String                           @id
  amount                         Float
  amount_cents                   Int
  settled_balance                Float
  settled_balance_cents          Int
  local_amount                   Float
  local_amount_cents             Int
  side                           String
  operation_type                 String
  currency                       String
  local_currency                 String
  label                          String
  settled_at                     DateTime
  emitted_at                     DateTime
  updated_at                     DateTime
  status                         String
  note                           String?                          @db.Text
  reference                      String?
  vat_amount                     Float?
  vat_amount_cents               Int?
  vat_rate                       Float?
  initiator_id                   String?
  attachment_lost                Boolean
  attachment_required            Boolean
  card_last_digits               String?
  category                       String
  iban                           String?
  QontoAttachments               QontoTransactionAttachment[]
  QontoTransactionLabels         QontoTransactionLabel[]
  StripePayoutToQontoTransaction StripePayoutToQontoTransaction[]
  InvoiceToQontoTransaction      InvoiceToQontoTransaction[]
  ignore_for_mapping             Boolean                          @default(false)
}

model StripePayoutToQontoTransaction {
  stripePayout        StripePayout     @relation(fields: [stripePayoutId], references: [id])
  stripePayoutId      String
  qontoTransaction    QontoTransaction @relation(fields: [qontoTransaction_id], references: [transaction_id])
  qontoTransaction_id String

  @@unique([stripePayoutId, qontoTransaction_id])
}

model InvoiceToQontoTransaction {
  invoice             Invoice          @relation(fields: [invoiceId], references: [id])
  invoiceId           String
  qontoTransaction    QontoTransaction @relation(fields: [qontoTransaction_id], references: [transaction_id])
  qontoTransaction_id String

  @@unique([invoiceId, qontoTransaction_id])
}

model QontoAttachment {
  id                          String                       @id
  created_at                  DateTime
  file_name                   String
  file_size                   String
  file_content_type           String
  url                         String
  QontoTransactionAttachments QontoTransactionAttachment[]
  QontoProbativeAttachment    QontoProbativeAttachment?
}

model QontoTransactionAttachment {
  transaction   QontoTransaction @relation(fields: [transactionId], references: [transaction_id])
  transactionId String
  attachment    QontoAttachment  @relation(fields: [attachmentId], references: [id])
  attachmentId  String

  @@id([transactionId, attachmentId])
}

model QontoLabel {
  id                     String                  @id
  name                   String
  parent_id              String?
  QontoTransactionLabels QontoTransactionLabel[]
}

model QontoTransactionLabel {
  transaction   QontoTransaction @relation(fields: [transactionId], references: [transaction_id])
  transactionId String
  label         QontoLabel       @relation(fields: [labelId], references: [id])
  labelId       String

  @@id([transactionId, labelId])
}

model QontoProbativeAttachment {
  local_id          String          @id
  status            String
  file_name         String
  file_size         String
  file_content_type String
  url               String
  attachment        QontoAttachment @relation(fields: [attachmentId], references: [id])
  attachmentId      String          @unique
}

model AgGridView {
  id          String  @id @unique @default(cuid())
  gridId      String  @db.VarChar(255)
  columnState String  @db.Text
  filterModel String  @db.Text
  chartModels String  @default("") @db.Text
  name        String  @db.VarChar(255)
  description String? @db.Text
  deleted     Boolean @default(false)
  userId      String? @map("userId")
  user        User?   @relation(fields: [userId], references: [id])
  ouId        String? @map("ouId")
  ou          Ou?     @relation(fields: [ouId], references: [id])
}

enum CreditTarifType {
  FIX
  FLEX
}

enum PowerType {
  AC
  DC
}

model CreditTarif {
  id                  String               @id @default(cuid())
  name                String
  tarifType           CreditTarifType
  powerType           PowerType
  validFrom           DateTime
  validTo             DateTime?
  sessionCredit       Float                @default(0)
  energyCredit        Float                @default(0)
  blockingCredit      Float                @default(0)
  maxBlockingCredit   Float                @default(0)
  blockingFeeMinStart Int                  @default(0)
  contacts            ContactCreditTarif[]
  kindOfTarif         KindOfTarif          @default(CREDIT)
}

model ContactCreditTarif {
  contactId     String
  contact       Contact     @relation(fields: [contactId], references: [id])
  creditTarifId String
  creditTarif   CreditTarif @relation(fields: [creditTarifId], references: [id])

  @@id([contactId, creditTarifId])
}

model CPOContract {
  id                      String    @id @default(cuid())
  name                    String    @default("Standard")
  numACCharger            Int       @default(0)
  numDCCharger            Int       @default(0)
  numACHotline            Int       @default(0)
  numDCHotline            Int       @default(0)
  priceACCharger          Float     @default(5)
  priceDCCharger          Float     @default(10)
  priceACHotline          Float     @default(5)
  priceDCHotline          Float     @default(5)
  start                   DateTime
  end                     DateTime
  contactId               String?
  contact                 Contact?  @relation(fields: [contactId], references: [id])
  kwhFeeCent              Float     @default(3) // Legacy field - will be deprecated
  kwhFeeCentAC            Float     @default(3)
  kwhFeeCentDC            Float     @default(3)
  sessionFeeCent          Float     @default(20) // Legacy field - will be deprecated
  sessionFeeCentAC        Float     @default(20)
  sessionFeeCentDC        Float     @default(20)
  monthlyEmployeeClubCharging Float     @default(0) // Preis in Euro
  directPaymentFeePercent Float     @default(3.5)
  directPaymentToken      String    @default("none")
  adhocPaymentFeePercent  Float     @default(3.5)
  serviceFeePerAC         Float     @default(0)
  serviceFeePerDC         Float     @default(0)
  invoices                Invoice[] // Beziehung zu Invoice
  files                   FileRef[]
  directPaymentContractId String   @default("none")
  tarifs                  Tarif[]
}

model EnerchargeSession {
  ID                         Int
  SessionID                  String
  KundenID                   String
  KundenID2                  String
  ChargePointNR              Int
  EVSEID                     String
  Ladetype                   Int
  Start_Session              DateTime
  End_Session                DateTime
  StartEnergieinhalt         Float
  EndeEnergieinhalt          Float
  Energy_content             Float
  Parktime                   Int
  Chargetime                 Int
  Preis                      Float
  payment_method_AuswahltId  Int
  payment_method_Auswahlt    String
  Billnr_Payment             Int
  Billnr_Gesamt              Int
  tariff_model               Int
  TA_NR                      String
  Payment_Kartennummer       String
  Payment_Zahlungstyp        String
  OCPP_TransactionID         String
  Selbstabrechnung           Boolean
  Teilstorno_Versuche        Int
  PDF_Update_Finish          Boolean
  Webcode                    String
  Session_StatusCode         Int
  Session_Status             String
  SessionIDMes               String?
  @@id([ID, SessionID])
}

model PayoneSession {
  Zeilenart                       String  @map("Zeilenart")
  Zahlref_Nr                      String      @map("Zahlref.-Nr.") // intColumns
  VU_ZE                           String?  @map("VU ZE")
  Kundennr_ZE                     String?  @map("Kundennr ZE")
  Name_ZE                         String?  @map("Name ZE")
  PLZ_ZE                          String?  @map("PLZ ZE")
  Ort_ZE                          String?  @map("Ort ZE")
  Strasse_ZE                      String?  @map("Straße ZE")
  VU                              String?  @map("VU")
  Kundennr_VU                     String?  @map("Kundennr VU")
  Name_VU                         String?  @map("Name VU")
  PLZ_VU                          String?  @map("PLZ VU")
  Ort_VU                          String?  @map("Ort VU")
  Strasse_VU                      String?  @map("Straße VU")
  MCC_Code                        String?       @map("MCC Code") // intColumns
  Kartenart                       String?  @map("Kartenart")
  K_Prod_Separate_Pricing         String?  @map("K.Prod Separate Pricing")
  Transaktion                     String?  @map("Transaktion")
  Terminal_ID                     String?       @map("Terminal-ID") // intColumns
  Kaufdatum                       DateTime? @map("Kaufdatum") // dateColumns
  Kaufdatum_Uhrzeit               String?  @map("Kaufdatum Uhrzeit")
  Processing_Datum                DateTime? @map("Processing Datum") // dateColumns
  Kartennr                        String  @map("Kartennr.")
  Waehrung                        String?  @map("Währung")
  Betrag_TXN                      Float    @map("Betrag TXN") // decimalColumns
  CashBack_Betrag                 String?  @map("CashBack Betrag")
  Anzahl                          String?  @map("Anzahl")
  Text_Summe                      String?  @map("Text Summe")
  Summe_Serv_Geb                  String?  @map("Summe Serv.Geb.")
  Summe_TXN_Geb                   String?  @map("Summe TXN-Geb.")
  Haendlerentgelt_in_Zahlwaehrung   Float  @map("Händlerentgelt in Zahlwährung") // decimalColumns
  Einzel_IC_in_Zahlwaehrung       Float    @map("Einzel-IC in Zahlwährung") // decimalColumns
  Summe_Interchange               String?  @map("Summe Interchange")
  Acq_Serv_Geb                    String?  @map("Acq. Serv. Geb")
  Scheme_Fee                      String?  @map("Scheme Fee")
  Netto_PLV_TXN                   String?  @map("Netto PLV TXN")
  Netto_Pre_Auth                  String?  @map("Netto Pre Auth")
  Netto_PLV_non_TXN               String?  @map("Netto PLV non TXN")
  Sicherheitseinbehalt_Betrag       String? @map("Sicherheitseinbehalt Betrag")
  SicherheitseinbehaltFaelligkeit   String? @map("SicherheitseinbehaltFälligkeit")
  Nettobetrag_Geb                 String?  @map("Nettobetrag Geb.")
  Ust_Geb                         String?  @map("Ust. Geb.")
  Bruttobetrag_Geb                String?  @map("Bruttobetrag Geb.")
  Netto                           String?  @map("Netto")
  Auszahlung_an_Vertragsnr        String?  @map("Auszahlung an Vertragsnr.")
  Bankname                        String?  @map("Bankname")
  Konto_IBAN                      String?  @map("Konto (IBAN)")
  BLZ_BIC                         String?  @map("BLZ (BIC)")
  Genehmigungsnr_TXN              String?      @map("Genehmigungsnr.TXN") // intColumns
  DCC_TXN_Betrag                  String?  @map("DCC TXN Betrag")
  DCC_CurrencyCode                String?  @map("DCC CurrencyCode")
  MerchantReference               String?  @map("MerchantReference")
  MessageText                     String?  @map("MessageText")
  Merchant_Voucher_No             String?       @map("Merchant Voucher No") // intColumns
  BSPCode                         String?  @map("BSPCode")
  CustomerOrderNo                 String?  @map("CustomerOrderNo")
  OrderInvoiceNumber              String?  @map("OrderInvoiceNumber")
  PassangerName                   String?  @map("PassangerName")
  MerchantPurchaseRef             String?  @map("MerchantPurchaseRef")
  TravelAgencyCode                String?  @map("TravelAgencyCode")
  Agency_Nr                       String?  @map("Agency Nr.")
  TravelAgencyName                String?  @map("TravelAgencyName")
  Ticket_Nr                       String?  @map("Ticket Nr.")
  Buyer_VAT_No                    String?  @map("Buyer VAT No.")
  Umrechnungskurs                 String?  @map("Umrechnungskurs")
  @@id([Kartennr, Zeilenart,Zahlref_Nr])
}

model MonthlyForecastAggregation {
  id                    String   @id @default(cuid())
  month                 String   // Format: "YYYY-MM"
  ouCode                String   // OU Code for filtering
  revenue               Float    // Total revenue for the month
  energyCost            Float    // Total energy cost for the month
  energyGrossMargin     Float    // Revenue - Energy Cost
  thgRevenue            Float    // THG revenue (kWh * 0.05)
  totalGrossMargin      Float    // Energy Margin + THG
  kWh                   Float    // Total kWh for the month
  sessions              Int      // Total sessions for the month
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@unique([month, ouCode])
  @@index([month])
  @@index([ouCode])
}