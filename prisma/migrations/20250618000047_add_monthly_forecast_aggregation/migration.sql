-- CreateTable
CREATE TABLE `MonthlyForecastAggregation` (
    `id` VARCHAR(191) NOT NULL,
    `month` VARCHAR(191) NOT NULL,
    `ouCode` VARCHAR(191) NOT NULL,
    `revenue` DOUBLE NOT NULL,
    `energyCost` DOUBLE NOT NULL,
    `energyGrossMargin` DOUBLE NOT NULL,
    `thgRevenue` DOUBLE NOT NULL,
    `totalGrossMargin` DOUBLE NOT NULL,
    `kWh` DOUBLE NOT NULL,
    `sessions` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `MonthlyForecastAggregation_month_idx`(`month`),
    INDEX `MonthlyForecastAggregation_ouCode_idx`(`ouCode`),
    UNIQUE INDEX `MonthlyForecastAggregation_month_ouCode_key`(`month`, `ouCode`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
