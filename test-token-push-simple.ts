/**
 * Einfaches TypeScript Test Script für Token Push API
 * Pusht den Token "XX123456" für die OU "Gesellschaft für Gestaltung"
 *
 * Ausführung:
 * - npx tsx test-token-push-simple.ts
 * - oder: node --loader tsx test-token-push-simple.ts
 */

// ===== KONFIGURATION =====
const API_BASE_URL = "http://localhost:3000";
const API_KEY = "EUL_03e3dc03d8cd09e2f51865f6d914819a"; // <-- R & M Beteiligungsgesellschaft mbH API-KEY
const TARGET_OU = "R & M Beteiligungsgesellschaft mbH";

// Test Token Daten
const TEST_TOKEN = {
  name: "Test Token R & M Beteiligungsgesellschaft mbH",
  uid: "R123456",
  note: "Test Token erstellt via TypeScript Script",
};

// ===== HELPER FUNCTIONS =====
const log = {
  info: (msg: string) => console.log(`🔍 ${msg}`),
  success: (msg: string) => console.log(`✅ ${msg}`),
  error: (msg: string) => console.log(`❌ ${msg}`),
  warning: (msg: string) => console.log(`⚠️ ${msg}`),
};

// ===== API FUNCTIONS =====
async function testApiKey(): Promise<boolean> {
  log.info("Teste API-Key Validierung...");

  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/test/apikey`, {
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const error = await response.text();
      log.error(`API-Key Validierung fehlgeschlagen (${response.status}): ${error}`);
      return false;
    }

    const data = await response.json();
    log.success("API-Key Validierung erfolgreich");

    console.log("📋 Contact Info:", {
      id: data.contact?.id,
      name: data.contact?.name,
      companyName: data.contact?.companyName,
      ou: data.contact?.ou?.name,
    });

    // OU prüfen
    if (data.contact?.ou?.name === TARGET_OU) {
      log.success(`Korrekte OU gefunden: "${TARGET_OU}"`);
      return true;
    } else {
      log.error(`Falsche OU: "${data.contact?.ou?.name}" (erwartet: "${TARGET_OU}")`);
      return false;
    }
  } catch (error) {
    log.error(`Netzwerk-Fehler: ${error}`);
    return false;
  }
}

async function pushToken(): Promise<boolean> {
  log.info("Pushe Token XX123456...");

  const payload = {
    tokens: [TEST_TOKEN],
  };

  console.log("📦 Token Daten:", JSON.stringify(payload, null, 2));

  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/contact/tokens`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();

    if (!response.ok) {
      log.error(`Token Push fehlgeschlagen (${response.status}): ${data.message || data.error}`);
      return false;
    }

    log.success("Token Push erfolgreich!");

    // Ergebnisse anzeigen
    console.log("📊 Ergebnis:", {
      message: data.message,
      tokenGroup: data.tokenGroup?.name,
      totalTokens: data.statistics?.total,
      processedTokens: data.statistics?.processed,
      longshipPushed: data.statistics?.longshipPushed,
      errors: data.statistics?.errors,
    });

    // Verarbeitete Tokens
    if (data.processedTokens?.length > 0) {
      console.log("\n📝 Verarbeitete Tokens:");
      data.processedTokens.forEach((token: any) => {
        console.log(`  - ${token.name} (${token.authenticationId}) - ${token.action}`);
      });
    }

    // Longship Erfolge
    if (data.longship?.success?.length > 0) {
      console.log("\n🌐 Longship Push erfolgreich:");
      data.longship.success.forEach((token: any) => {
        console.log(
          `  - ${token.name} (${token.uid}) -> TokenGroup: ${token.longshipTokenGroupId}`,
        );
      });
    }

    // Fehler anzeigen
    if (data.errors?.length > 0) {
      log.warning("Lokale Fehler:");
      data.errors.forEach((error: any) => {
        console.log(`  - ${error.name} (${error.uid}): ${error.error}`);
      });
    }

    if (data.longship?.errors?.length > 0) {
      log.warning("Longship Fehler:");
      data.longship.errors.forEach((error: any) => {
        console.log(`  - ${error.name || error.uid || "Unknown"}: ${error.error || error}`);
      });
    }

    return true;
  } catch (error) {
    log.error(`Netzwerk-Fehler: ${error}`);
    return false;
  }
}

// ===== MAIN FUNCTION =====
async function main(): Promise<void> {
  console.log("🧪 Token Push Test Script");
  console.log("=".repeat(50));

  // API-Key prüfen
  if (API_KEY === "YOUR_API_KEY_HERE") {
    log.error("Bitte setzen Sie einen gültigen API-Key in der Variable API_KEY");
    process.exit(1);
  }

  try {
    // 1. API-Key validieren
    const isValidApiKey = await testApiKey();
    if (!isValidApiKey) {
      log.error("API-Key Validierung fehlgeschlagen. Script wird beendet.");
      process.exit(1);
    }

    console.log("");

    // 2. Token pushen
    const isPushSuccessful = await pushToken();

    console.log("");
    if (isPushSuccessful) {
      log.success("🎉 Test erfolgreich abgeschlossen!");
      process.exit(0);
    } else {
      log.error("Test fehlgeschlagen!");
      process.exit(1);
    }
  } catch (error) {
    log.error(`Unerwarteter Fehler: ${error}`);
    process.exit(1);
  }
}

// Script ausführen
main();
