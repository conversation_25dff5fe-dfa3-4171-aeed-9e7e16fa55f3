/**
 * Test Script für Charging Power API
 * Testet den Endpunkt /api/v1/ou/charging-power
 * 
 * Ausführung: npx tsx test-charging-power.ts
 */

// ===== KONFIGURATION =====
const API_BASE_URL = "http://localhost:3000";
const API_KEY = "EUL_03e3dc03d8cd09e2f51865f6d914819a"; // R & M Beteiligungsgesellschaft mbH API-Key

// ===== HELPER FUNCTIONS =====
const log = {
  info: (msg: string) => console.log(`🔍 ${msg}`),
  success: (msg: string) => console.log(`✅ ${msg}`),
  error: (msg: string) => console.log(`❌ ${msg}`),
  warning: (msg: string) => console.log(`⚠️ ${msg}`),
};

// ===== API FUNCTIONS =====
async function testChargingPowerEndpoint(): Promise<boolean> {
  log.info('Teste Charging Power Endpunkt...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/ou/charging-power`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const error = await response.text();
      log.error(`Charging Power Endpunkt fehlgeschlagen (${response.status}): ${error}`);
      return false;
    }

    const data = await response.json();
    log.success('Charging Power Endpunkt erfolgreich!');
    
    // Basis-Informationen anzeigen
    console.log('\n📋 Contact Info:', {
      id: data.contact?.id,
      name: data.contact?.name,
      companyName: data.contact?.companyName,
      ou: data.contact?.ou?.name
    });

    // Ladeleistungs-Übersicht
    console.log('\n⚡ Ladeleistungs-Übersicht:');
    console.log(`  Gesamte aktuelle Ladeleistung: ${data.chargingPower?.totalCurrentKw} kW`);
    console.log(`  Aktive Ladesessions: ${data.chargingPower?.totalActiveSessions}`);
    console.log(`  Ladepunkte in Betrieb: ${data.chargingPower?.totalChargingPoints}`);

    // Statistiken
    console.log('\n📊 Statistiken:');
    console.log(`  Geprüfte OUs: ${data.statistics?.totalOusChecked}`);
    console.log(`  OUs mit aktiven Sessions: ${data.statistics?.ousWithActiveSessions}`);

    // Power nach OU
    if (data.chargingPower?.powerByOu?.length > 0) {
      console.log('\n🏢 Ladeleistung nach OU:');
      data.chargingPower.powerByOu.forEach((ou: any) => {
        console.log(`  - ${ou.ouName} (${ou.ouCode}): ${ou.currentKw.toFixed(2)} kW (${ou.activeSessions} Sessions)`);
      });
    } else {
      log.warning('Keine aktiven Ladesessions gefunden');
    }

    // Detaillierte Ladepunkt-Informationen
    if (data.chargingPower?.chargingPointDetails?.length > 0) {
      console.log('\n🔌 Aktive Ladepunkte:');
      data.chargingPower.chargingPointDetails.forEach((cp: any, index: number) => {
        console.log(`  ${index + 1}. ${cp.evseId} (${cp.chargePointId})`);
        console.log(`     Aktuelle Leistung: ${cp.currentKw.toFixed(2)} kW`);
        console.log(`     Geladene Energie: ${cp.totalEnergyInKwh.toFixed(2)} kWh`);
        console.log(`     Location: ${cp.location?.name || 'Unbekannt'}`);
        console.log(`     OU: ${cp.ou?.name || 'Unbekannt'}`);
        console.log(`     Session Start: ${new Date(cp.sessionStartTime).toLocaleString('de-DE')}`);
        console.log('');
      });
    }

    // JSON-Response für Debugging (optional)
    if (process.argv.includes('--debug')) {
      console.log('\n🐛 Debug - Vollständige Response:');
      console.log(JSON.stringify(data, null, 2));
    }

    return true;
  } catch (error) {
    log.error(`Netzwerk-Fehler: ${error}`);
    return false;
  }
}

// ===== MAIN FUNCTION =====
async function main(): Promise<void> {
  console.log('⚡ Charging Power API Test');
  console.log('='.repeat(50));
  
  try {
    const isSuccessful = await testChargingPowerEndpoint();
    
    console.log('');
    if (isSuccessful) {
      log.success('🎉 Test erfolgreich abgeschlossen!');
      console.log('\n💡 Tipps:');
      console.log('  - Verwenden Sie --debug für vollständige JSON-Response');
      console.log('  - Der Endpunkt zeigt nur aktive Ladesessions');
      console.log('  - Ladeleistung wird in Echtzeit aus den Session-Daten berechnet');
      process.exit(0);
    } else {
      log.error('Test fehlgeschlagen!');
      process.exit(1);
    }
  } catch (error) {
    log.error(`Unerwarteter Fehler: ${error}`);
    process.exit(1);
  }
}

// Script ausführen
main();
