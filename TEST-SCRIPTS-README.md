# Token Push Test Scripts

Diese Scripts testen den Token Push API-Endpunkt `/api/v1/contact/tokens` und pushen den Test-Token "XX123456" für die OU "Gesellschaft für Gestaltung".

## 📁 Verfügbare Scripts

### 1. `test-token-push.ts` (Vollständig typisiert)
- Vollständig typisiertes TypeScript mit Interfaces
- Detaillierte Fehlerbehandlung und Logging
- Farbige Konsolen-Ausgabe

### 2. `test-token-push-simple.ts` (Einfach)
- Einfachere Version ohne komplexe Types
- Weniger Code, gleiche Funktionalität
- Gut für schnelle Tests

## 🚀 Verwendung

### Voraussetzungen
```bash
# Node.js 18+ erforderlich
node --version

# TypeScript Executor installieren (global)
npm install -g tsx
```

### Setup
1. **API-Key eintragen**: Öffnen Sie das gewünschte Script und setzen Sie Ihren API-Key:
   ```typescript
   const API_KEY = 'EUL_your_actual_api_key_here';
   ```

2. **URL anpassen** (falls nötig):
   ```typescript
   const API_BASE_URL = 'https://your-domain.com'; // oder http://localhost:3000
   ```

### Ausführung

#### Option 1: Mit tsx (empfohlen)
```bash
# Vollständiges Script
npx tsx test-token-push.ts

# Einfaches Script
npx tsx test-token-push-simple.ts
```

#### Option 2: Mit Node.js (18+)
```bash
node --loader tsx test-token-push-simple.ts
```

#### Option 3: Mit npm scripts
```bash
# package.json umbenennen
mv test-scripts-package.json package.json

# Dependencies installieren
npm install

# Scripts ausführen
npm run test:token        # Vollständiges Script
npm run test:token-simple # Einfaches Script
npm run test:node         # Mit Node.js Loader
```

## 📋 Was die Scripts testen

### 1. API-Key Validierung
- Testet den Endpunkt `/api/v1/test/apikey`
- Prüft ob der API-Key gültig ist
- Verifiziert dass der Contact zur OU "Gesellschaft für Gestaltung" gehört

### 2. Token Push
- Sendet Token-Daten an `/api/v1/contact/tokens`
- Erstellt/aktualisiert Token in der lokalen Datenbank
- Pusht Token automatisch zu Longship
- Zeigt detaillierte Ergebnisse an

## 📊 Beispiel-Output

```
🧪 Token Push Test Script
==================================================
🔍 Teste API-Key Validierung...
✅ API-Key Validierung erfolgreich
📋 Contact Info: {
  id: 'contact-id-123',
  name: 'Test Contact',
  companyName: 'Test Company',
  ou: 'Gesellschaft für Gestaltung'
}
✅ Korrekte OU gefunden: "Gesellschaft für Gestaltung"

🔍 Pushe Token XX123456...
📦 Token Daten: {
  "tokens": [
    {
      "name": "Test Token Gesellschaft für Gestaltung",
      "uid": "XX123456",
      "note": "Test Token erstellt via TypeScript Script"
    }
  ]
}
✅ Token Push erfolgreich!
📊 Ergebnis: {
  message: '1 Token(s) erfolgreich verarbeitet',
  tokenGroup: 'Gesellschaft für Gestaltung',
  statistics: { total: 1, processed: 1, errors: 0, longshipPushed: 1, longshipErrors: 0 }
}

📝 Verarbeitete Tokens:
  - Test Token Gesellschaft für Gestaltung (XX123456) - created

🌐 Longship Push erfolgreich:
  - Test Token Gesellschaft für Gestaltung (XX123456) -> TokenGroup: longship-group-id-123

✅ 🎉 Test erfolgreich abgeschlossen!
```

## 🔧 Anpassungen

### Anderen Token testen
```typescript
const TEST_TOKEN = {
  name: "Mein Test Token",
  uid: "MEINE_UID_123",
  note: "Meine Notiz"
};
```

### Andere OU testen
```typescript
const TARGET_OU = 'Andere OU Name';
```

### Mehrere Tokens gleichzeitig
```typescript
const payload = {
  tokens: [
    { name: "Token 1", uid: "UID001", note: "Erster Token" },
    { name: "Token 2", uid: "UID002", note: "Zweiter Token" },
    { name: "Token 3", uid: "UID003", note: "Dritter Token" }
  ]
};
```

## 🐛 Troubleshooting

### "API-Key Validierung fehlgeschlagen"
- Prüfen Sie ob der API-Key korrekt gesetzt ist
- Prüfen Sie ob der Contact zur richtigen OU gehört
- Prüfen Sie ob der API-Server erreichbar ist

### "Falsche OU gefunden"
- Der API-Key gehört zu einem Contact in einer anderen OU
- Verwenden Sie einen API-Key für die OU "Gesellschaft für Gestaltung"

### "Longship Fehler"
- Longship API ist nicht erreichbar
- Keine passende TokenGroup in Longship gefunden
- OU Code stimmt nicht überein

### TypeScript Fehler
```bash
# tsx installieren
npm install -g tsx

# Oder lokal installieren
npm install tsx typescript @types/node
```
